from configparser import ConfigParser
import asyncio
import struct 
import re
from datetime import datetime, timedelta, date
import os
import duckdb
import fsspec
import json
from messaging import component
import nats.errors
import nats.js.errors
import logging
from logging.handlers import RotatingFileHandler
import os
from config import app_config

"""
This module reads the realtime alarms that are not acknowledged and the archive of the current month for all mapped plants, 
and publish them to the nats kv store "alarms" bucket, "iset.alarms.{year}.{month}" and "iset.panel.alarms" for pending alarms.
""" 
toml_config = app_config('config.toml')
# create log folder
if not os.path.isdir("log"):
    os.makedirs('log')

#logger
logger = logging.getLogger('isetalarms')
logger.setLevel(logging.INFO)

# Create a rotating file handler
log_file = f"./log/isetalarms.log"
handler = RotatingFileHandler(log_file, maxBytes=1024*1024*50, backupCount=2)
formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
handler.setFormatter(formatter)

# Console handler
console_handler = logging.StreamHandler()
console_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(handler)
logger.addHandler(console_handler)


OLE_TIME_ZERO = datetime(1899, 12, 30, 0, 0, 0)
SPKDIR = 'dirspk'
DATADIR = 'dirdati'
fs = fsspec.filesystem('memory')

def Ole2PosixTS(oledt):
    """
    this method converts Ole timestamp to an epoch timestamp
    """    
    if oledt == 0:
        return datetime.min
    tz_offset = datetime.now().astimezone().utcoffset()     
    oletime = OLE_TIME_ZERO + timedelta(days=oledt) #+ tz_offset    
    #return oletime.strftime('%c')    
    return oletime #oletime.timestamp() 

def get_config(path):
    Engines = {}
    try:
        configure = ConfigParser()
        iset_server_config = configure.read(path)
        for key in configure.sections():
                Engines[key] = {}
                for values in configure[key]:
                    Engines[key][values]= configure[key][values]
        Engines.pop('Sezioni')                    
    except Exception as e:
        logger.error(e)
    return Engines

def json_serial(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, (datetime, date)):
        return obj.strftime("%Y-%m-%d %H:%M:%S")
    raise TypeError ("Type %s not serializable" % type(obj))

def extract_alarm(engine,path): 
     """
     Alarm file extraction method 
     """   
     print('reading',path)
     alarms = {}  
     if os.path.exists(path) == 0:
        return alarms
     index = 0
     try:
        with open(path,'rb') as file:
            while True:
                #records reading
                try:                    
                    #file.read(13)
                    id = f'{engine}::{int.from_bytes(file.read(4), "little")}'
                    file.read(264)
                    timestamp = Ole2PosixTS(struct.unpack('d', file.read(8))[0])
                    n_tentativi = file.read(4)
                    index = int.from_bytes(file.read(4), "little")
                    file.read(5)

                    #file.read(13)
                    description = re.sub(r'[\x00-\x1f]', '', file.read(25).decode()).strip()
                    level = int.from_bytes(file.read(1), "little")
                    file.read(7)

                    #if timestamp >=  (datetime.now() - timedelta(days = 60)):
                    #print(timestamp,id,level,description)
                    alarms[index] = {
                        'engine': engine,
                        'tag' : id,
                        'timestamp' : timestamp,
                        'description' : description,                       
                        'level' : level,
                        'indice' : index
                        }
                    index += 1

                except Exception as e:
                    #print(engine,e)  
                    return alarms 
                          
     except Exception as e:
         print(e)
         pass
         
     return alarms 

def alarm_archive(engine,path): 
     """
     Alarm file extraction method 
     """     
     print(path)
     archive = dict()
     DESCRIPTION_LENGTH = 26
     if os.path.getsize(path) == 0:
        print("file is empty")
        return
     try:
        with open(path,"rb") as file: 
            i = 0
            #Header  reading
            '''
            VerMaj = int.from_bytes(file.read(1), "little")
            VerMin =  int.from_bytes(file.read(1), "little")
            LastRec = struct.unpack('<I', file.read(4))[0]#int.from_bytes(file.read(4), "big")
            LastRec_date =datetime.fromtimestamp(LastRec).strftime('%c')
            RecLen = int.from_bytes(file.read(2), "little")
            PosData = int.from_bytes(file.read(1), "little")
            LenData = int.from_bytes(file.read(1), "little")
            Status = int.from_bytes(file.read(1), "little")
            name = file.read(67).decode("utf-8") 
            '''
            file.read(78)
            while True:
                #records reading
                try:
                    timestamp = Ole2PosixTS(struct.unpack('d', file.read(8))[0])
                except:
                    return archive
                    break
                if timestamp == 0:
                    return archive
                    break
                
                try:
                    id = int.from_bytes(file.read(4), "little")
                    device = int.from_bytes(file.read(4), "little")
                    alarmtype = int.from_bytes(file.read(4), "little")
                    description = re.sub(r'[\x00-\x1f]', '', file.read(DESCRIPTION_LENGTH).decode())
                    severity = int.from_bytes(file.read(4), "little")
                    ackid = int.from_bytes(file.read(4), "little")
                    source = int.from_bytes(file.read(4), "little")
                    resumets = Ole2PosixTS(struct.unpack('d', file.read(8))[0])
                    ackts =   Ole2PosixTS(struct.unpack('d', file.read(8))[0])     
                    recnum = int.from_bytes(file.read(4), "little") 
                    id = f"{engine}::{id}"          
                except Exception as e:
                    logger.error(e) 
                                
                archive[i] = {
                            "timestamp" : timestamp,
                            "livello" : severity,
                            "tag" : id,
                            "messaggio" : description.strip(),
                            "tipo_allarme" : alarmtype,                    
                            "ack_timestamp" : ackts,
                            "ackid" : ackid,
                            "source" : source,
                            "record_number" : recnum                    
                    }
                i += 1
     except:
         return archive
     return archive

class Appstate:
    pass

async def alarms():
    logger.info('iset alarms reader started')
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    state = Appstate()
    state.nc = component(toml_config['nats'])

    async def connect():
        try:
            logger.info('connectiong to nats...')
            await state.nc.connect(bucket = 'iset')             
            state.alarmskv = await state.nc.key_value(bucket = 'alarms')
            state.configwatcher = await state.nc.watchkv('iset.config.>')            
        except Exception as e:
            await asyncio.sleep(1)
            logger.error(e)
            await connect()
    await connect()
    isetconfig = dict()
    while True:
        alarms = list()
        archive = list()
        today = datetime.today()
        year = today.year
        yearsliced = today.strftime('%y')
        month = f'{today.month:0>1X}'
        # hydrate config store    
        loaded = False     
        try:
            ''''''
            isetkv = await state.configwatcher.updates(timeout=2)
            if isetkv is not None:
                data = json.loads(isetkv.value.decode())         
                if ('Tag' in data):
                    isetconfig[data['Tag']] = data
                else:
                     isetconfig[isetkv.key] = data
        except nats.errors.TimeoutError as e:
            loaded = True
            pass
        except Exception as e:
            print('config loop error',e)

        if loaded :
            #---------------------------------- REALTIME ALARMS
            for engine,path in cfg.items():
                try:
                    result = extract_alarm(engine,f"{path[SPKDIR]}Allarmi.dat")

                    for _,record in result.items() :
                        if record['tag'] in isetconfig:
                            record['canale'] = isetconfig[record['tag']].get('Nome',record['tag'])
                            record['plant'] = isetconfig[record['tag']].get('Plant',record['tag'])
                        else:
                            pass
                            # uncomment to see these in the realtime alarms
                            #record['canale'] = record['tag']
                            #record['plant'] = record['engine']
                        alarms.append(record)
                    
                    try:
                        archive_path = f"{path[DATADIR]}{year}\\ALL_BIN.{month}{yearsliced}"
                        archive_result = alarm_archive(engine,archive_path)
                        
                        for _,record in archive_result.items():
                            record['periferica'] = engine
                            record['address'] = record['tag']
                            if record['tag'] in isetconfig:                            
                                record['periferica'] = isetconfig[record['tag']]['Plant']
                                record['tag'] = isetconfig[record['tag']]['Nome']

                            archive.append(record)
                    except:
                        pass
                        
                except Exception as e:
                    print(e)
            try:
                with duckdb.connect() as conn:
                    # Create a memory filesystem and write the dictionary data to it
                    
                    table_name = 'rtalarms'

                    with fs.open(f'{table_name}.json', 'w') as file:
                        file.write(json.dumps(alarms,default=json_serial))

                    # Register the memory filesystem and create the table
                    conn.register_filesystem(fs)
                    conn.execute(f"CREATE TABLE IF NOT EXISTS {table_name} AS SELECT * FROM read_json_auto('memory://{table_name}.json')")
                    conn.sql(f"COPY {table_name}  TO 'memory://{table_name}.parquet'  (FORMAT 'parquet', CODEC 'zstd', USE_TMP_FILE 'false') ")

                    conn.sql(f"select * from read_parquet('memory://{table_name}.parquet') order by timestamp desc").show()
                    '''
                    with fs.open(f'{table_name}.parquet', 'rb') as file:
                        alarms = file.read()
                        await state.alarmskv.put(f'iset.panel.alarms',alarms)
                    '''

                    # Archive -----------------------
                    
                    table_name = 'archive'

                    with fs.open(f'{table_name}.json', 'w') as file:
                        file.write(json.dumps(archive,default=json_serial))

                    # Register the memory filesystem and create the table
                    conn.execute(f"CREATE TABLE IF NOT EXISTS {table_name} AS SELECT * FROM read_json_auto('memory://{table_name}.json')")
                    conn.sql(f"COPY {table_name}  TO 'memory://{table_name}.parquet'  (FORMAT 'parquet', CODEC 'zstd', USE_TMP_FILE 'false') ")

                    #conn.sql(f"select * from read_parquet('memory://{table_name}.parquet') order by timestamp desc").show()
                    '''
                    with fs.open(f'{table_name}.parquet', 'rb') as file:
                        archive_binary = file.read()
                        await state.alarmskv.put(f'iset.alarms.{year}.{int(month,16)}',archive_binary)     
                    '''           

                    with fs.open(f'archive.parquet', 'rb') as file:
                        archive_binary = file.read()
                        await state.alarmskv.put(f'iset.alarms.{year}.{int(month,16)}',archive_binary)  

                    with fs.open(f'rtalarms.parquet', 'rb') as file:
                        alarms = file.read()
                        await state.alarmskv.put('iset.panel.alarms',alarms) 

            except Exception as e:
                print(e)
                
      
        #await asyncio.sleep(1)
  

async def alarms_v2():
    logger.info('iset alarms reader started')
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    state = Appstate()
    state.nc = component(toml_config['nats'])

    async def connect():
        try:
            logger.info('connectiong to nats...')
            await state.nc.connect(bucket = 'iset')             
            state.alarmskv = await state.nc.key_value(bucket = 'alarms')
            #configwatcher = await state.nc.watchkv('iset.config.>')            
        except Exception as e:
            await asyncio.sleep(1)
            logger.error(e)
            await connect()
    await connect()
    isetconfig = dict()
    
    configwatcher = None
    config_loaded = False
    configwatcher = await state.nc.watchkv('iset.config.>')  
    while True:
        print(configwatcher)
        try:
            while configwatcher is None:
                try:
                    configwatcher = await state.nc.watchkv('iset.config.>')  
                except Exception as e:
                    logger.error(f'watcher error {e}')        
                    await asyncio.sleep(5)

            async for update in configwatcher:
                try:
                    data = json.loads(update.value.decode())         
                    if 'Tag' in data:
                        if update.operation == 'DEL':
                            del isetconfig[data['Tag']]
                        else:
                            isetconfig[data['Tag']] = data
                except:
                    pass    
            config_loaded = True       
        except nats.errors.TimeoutError as e:
            logger.error(f"Timeout error in watcher,{e}")
            #configwatcher  = None  # Reset watcher to recreate it
            await asyncio.sleep(5)
        except Exception as e:
            logger.error(f'watcher generic error,{e}')        

        alarms = list()
        archive = list()
        today = datetime.today()
        year = today.year
        yearsliced = today.strftime('%y')
        month = f'{today.month:0>1X}'

        if config_loaded :
            #---------------------------------- REALTIME ALARMS
            for engine,path in cfg.items():
                try:
                    result = extract_alarm(engine,f"{path[SPKDIR]}Allarmi.dat")

                    for _,record in result.items() :
                        if record['tag'] in isetconfig:
                            record['canale'] = isetconfig[record['tag']].get('Nome',record['tag'])
                            record['plant'] = isetconfig[record['tag']].get('Plant',record['tag'])
                        else:
                            pass
                            # uncomment to see these in the realtime alarms
                            #record['canale'] = record['tag']
                            #record['plant'] = record['engine']
                        alarms.append(record)
                    
                    try:
                        archive_path = f"{path[DATADIR]}{year}\\ALL_BIN.{month}{yearsliced}"
                        archive_result = alarm_archive(engine,archive_path)
                        
                        for _,record in archive_result.items():
                            record['periferica'] = engine
                            record['address'] = record['tag']
                            if record['tag'] in isetconfig:                            
                                record['periferica'] = isetconfig[record['tag']]['Plant']
                                record['tag'] = isetconfig[record['tag']]['Nome']

                            archive.append(record)
                    except:
                        pass
                        
                except Exception as e:
                    print(e)
            try:
                with duckdb.connect() as conn:
                    # Create a memory filesystem and write the dictionary data to it
                    
                    table_name = 'rtalarms'

                    with fs.open(f'{table_name}.json', 'w') as file:
                        file.write(json.dumps(alarms,default=json_serial))

                    # Register the memory filesystem and create the table
                    conn.register_filesystem(fs)
                    conn.execute(f"CREATE TABLE IF NOT EXISTS {table_name} AS SELECT * FROM read_json_auto('memory://{table_name}.json')")
                    conn.sql(f"COPY {table_name}  TO 'memory://{table_name}.parquet'  (FORMAT 'parquet', CODEC 'zstd', USE_TMP_FILE 'false') ")

                    conn.sql(f"select * from read_parquet('memory://{table_name}.parquet') order by timestamp desc").show()
                    with fs.open(f'{table_name}.parquet', 'rb') as file:
                        alarms = file.read()
                        await state.alarmskv.put(f'iset.panel.alarms',alarms)


                    # Archive -----------------------
                    """
                    table_name = 'archive'

                    with fs.open(f'{table_name}.json', 'w') as file:
                        file.write(json.dumps(archive,default=json_serial))

                    # Register the memory filesystem and create the table

                    conn.execute(f"CREATE TABLE IF NOT EXISTS {table_name} AS SELECT * FROM read_json_auto('memory://{table_name}.json')")
                    conn.sql(f"COPY {table_name}  TO 'memory://{table_name}.parquet'  (FORMAT 'parquet', CODEC 'zstd', USE_TMP_FILE 'false') ")

                    conn.sql(f"select * from read_parquet('memory://{table_name}.parquet') order by timestamp desc").show()
                    with fs.open(f'{table_name}.parquet', 'rb') as file:
                        alarms = file.read()
                        await state.alarmskv.put(f'iset.alarms.{year}.{int(month,16)}',alarms)                
                    """ 
            except Exception as e:
                print(e)
                
        #await asyncio.sleep(1)
        print('ciaooooo')
        
if __name__ == '__main__':
    asyncio.run(alarms())

