from configparser import ConfigParser
import asyncio
from datetime import datetime, timedelta
import struct
import time 
import duckdb
import fsspec
import fsspec.implementations.memory
import json
from messaging import component
import math 
import glob
import os
import fsspec
import numpy as np

import logging
from logging.handlers import RotatingFileHandler
import os
from config import app_config

toml_config = app_config('config.toml')

"""
this class is used to read ISET data from their proprietary format, all binary data has beed reversed enginereed to get the values and crosscheck them with the config files from iset
"""
# create log folder
if not os.path.isdir("log"):
    os.makedirs('log')

#logger
logger = logging.getLogger('iset')
logger.setLevel(logging.INFO)

# Create a rotating file handler
log_file = f"./log/iset.log"
handler = RotatingFileHandler(log_file, maxBytes=1024*1024*50, backupCount=2)
formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
handler.setFormatter(formatter)

# Console handler
console_handler = logging.StreamHandler()
console_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(handler)
logger.addHandler(console_handler)


CONFIGDIR = 'dirconfig'
DATADIR = 'dirdati'
LASTDIR = 'dirlast'
OLE_TIME_ZERO = datetime(1899, 12, 30, 0, 0, 0)

def tag_to_topic(tag):
    try:
        engine = tag.split("::")[0]
        tag_adr = tag.split("::")[1]

        NLogico = int(tag_adr[slice(0,len(tag_adr)-6, 1)])
        types =  ["AIN","DIN","AOUT","DOUT","SP"]
        type_idx = int(tag_adr[slice(len(tag_adr)-6, len(tag_adr)-4, 1)])
        type = types[type_idx]
        index =  int(tag_adr[slice(len(tag_adr)-4, len(tag_adr), 1)])

        topic = f"iset.config.{engine}.{NLogico}.{type}.{index}"
    except:
        topic = ""
    
    return topic

def Ole2PosixTS(oledt):
    """
    this method converts Ole timestamp to an epoch timestamp
    """    
    if oledt == 0:
        return 0
    tz_offset = datetime.now().astimezone().utcoffset()     
    oletime = OLE_TIME_ZERO + timedelta(days=oledt) #+ tz_offset    
    #return oletime.strftime('%c')    
    return oletime.timestamp() * 1000

def get_config(path):
    Engines = {}
    try:
        configure = ConfigParser()
        iset_server_config = configure.read(path)
        for key in configure.sections():
                Engines[key] = {}
                for values in configure[key]:
                    Engines[key][values]= configure[key][values]
        Engines.pop('Sezioni')                    
    except Exception as e:
        logger.warning(e)
    return Engines

def decode_last_raw(path,prefix)-> dict:
    data = dict()
    with open(path,'rb') as f:
        f.read(4)

        def index(i):
            index = f"000{i}"
            if i >= 10:
              index = f"00{i}"
            if i >= 100:
                index = f"0{i}"
            if i >= 1000:
                index = f"{i}"
            return index    
        
        #read analog inputs
        for i in range(1,257):
            key = f'{prefix}00{index(i)}'
            state = int.from_bytes(f.read(4), "little")
            value = round(struct.unpack('f', f.read(4))[0],2)
            data[key] = {'state': state, 'value' :value}

        #read digital inputs
        for i in range(1,1025):
            key = f'{prefix}01{index(i)}'
            state = int.from_bytes(f.read(4), "little")
            value = int.from_bytes(f.read(4), "little") 
            data[key] = {'state': state, 'value' :value}

        #read digital outputs
        for i in range(1,1025):
            key = f'{prefix}03{index(i)}'
            state = int.from_bytes(f.read(4), "little")
            value = int.from_bytes(f.read(4), "little")
            data[key] = {'state': state, 'value' :value}  
       
        #read analog outputs
        for i in range(1,257):
            key = f'{prefix}02{index(i)}'
            state = int.from_bytes(f.read(4), "little")
            value = round(struct.unpack('f', f.read(4))[0] ,2)
            data[key] = {'state': state, 'value' :value}        
        return data          

def extract_digital(path):
     """
     Digital Inputs/Outputs file reader
     """
     logger.info(path)
     file = open(path,"rb")     
     #useless header
     header = file.read(72)
     max_variables = 64 
     data = {}
     while True:
        channels = {}
        try:
            timestamp = Ole2PosixTS(struct.unpack('d', file.read(8))[0])
        except:
            break
        if timestamp == 0:
            break
        for index in range(0,max_variables):
            channels[index] = int.from_bytes(file.read(1))
        data[timestamp] = channels
     file.close() 
     return data

def extract_analog(path):
    """
    Analog Inputs/Outputs file reader
    """    
    logger.info(path)
    file = open(path,"rb")     
    #useless header
    header = file.read(72)    
    max_variables = 16
    data = {}    
    while True:
        channels = {}
        try:
            timestamp = Ole2PosixTS(struct.unpack('d', file.read(8))[0])
        except:
            break
        if timestamp == 0:
            break        
        for index in range(0,max_variables):    
            temp = round(struct.unpack('f', file.read(4))[0] ,2)
            if  not math.isnan(temp):
                channels[index] = temp 
                data[timestamp] = channels   
    file.close()     
    return data
      
def read_last(tags):
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    engines = {}
    for k,v in cfg.items():
        engines[k] = v[LASTDIR]

    #split tag to get engine and logical number
    files = {}
    data = {}
    for tag in tags:
        try:
            engine = tag.split("::")[0]
            tag_adr = tag.split("::")[1]
            NLogico = tag_adr[slice(0,len(tag_adr)-6, 1)]
            type_idx = tag_adr[slice(len(tag_adr)-6, len(tag_adr)-4, 1)]
            index =  tag_adr[slice(len(tag_adr)-4, len(tag_adr), 1)]                  
            prefix = f"{engine}::{NLogico}"
            
            key = f"{engine}_{NLogico}"
            if key not in files:
                path = f"{engines[engine]}\last{NLogico}.d50"
                files[key] = decode_last_raw(path,prefix)
            
            #if the tag is already present dont read any more files
            if tag in files[key]:
                data[tag] = files[key][tag]
                continue                
        except:
            continue
    return(data)

def read_hist(tags,start,end):
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    engines = {}
    for k,v in cfg.items():
        engines[k] = v[DATADIR]

    #split tag to get engine and logical number
    files = {}
    data = []

    #timebased iset file indexing
    start_date = start #datetime.strptime(start,"%d/%m/%Y %H:%M:%S")
    end_date = end #datetime.strptime(end,"%d/%m/%Y %H:%M:%S")

    year_difference = end_date.year - start_date.year
    month_difference = end_date.month - start_date.month    
    total_months = (year_difference * 12) + month_difference    
    year_month_list = [
        (start_date.year + (month // 12),f"{(month % 12) + 1:0>1X}{str(start_date.year + (month // 12))[-2:]}")
        for month in range(start_date.month - 1, start_date.month  + total_months)
    ]

    for elements in year_month_list:
        for tag in tags:
            try:
                engine = tag.split("::")[0]
                tag_adr = tag.split("::")[1]
                NLogico = int(tag_adr[slice(0,len(tag_adr)-6, 1)])
                type_idx = int(tag_adr[slice(len(tag_adr)-6, len(tag_adr)-4, 1)])
                index =  int(tag_adr[slice(len(tag_adr)-4, len(tag_adr), 1)])
                prefix = f"{engine}::{NLogico}"
            
                #time costraint
                year = elements[0]
                format = elements[1]                
                types =  ["IA","ID","UA","UD","SP"]
                
                #data type
                datatype = types[type_idx]   

                #logical number 
                if NLogico < 10 :
                    s_nlogico = f"00{NLogico}"
                elif NLogico >= 10 and NLogico < 100:
                    s_nlogico = f"0{NLogico}" 
                else:
                    s_nlogico = f"{NLogico}"  
            
                #banco dati           
                if datatype == "IA" or datatype == "UA": 
                    offset = int((index-1)/16)
                    if offset > 0 :
                        ch_number = (index-1) % (offset * 16)
                    else:
                        ch_number = (index-1)

                elif  datatype == "ID" or datatype == "UD":
                    offset = int((index-1)/64)
                    if offset > 0 :
                        ch_number = (index-1) % (offset * 64)
                    else:
                        ch_number = (index-1)
                
                if offset < 10:
                    banco = f"B0{offset}"
                if offset >= 10:
                    banco = f"B{offset}"

                filename = f"{datatype}{s_nlogico}{banco}.{format}"

                path = f"{engines[engine]}{year}\{filename}"
                
                if filename not in files.keys():
                    if datatype == "IA" or datatype == "UA": 
                        files[filename] = extract_analog(path)
                    elif  datatype == "ID" or datatype == "UD":
                        files[filename] = extract_digital(path)                        
                    time.sleep(0.001)
                #if the tag is already present dont read any more files
                '''
                if tag not in data:
                    data[tag] = {}
                '''
                oldvalue = None
                for ts in files[filename]:
                    dt = datetime.fromtimestamp(ts/1000)     
                    if dt >= start_date and dt <= end_date: 
                        value = files[filename][ts][ch_number] 
                        if oldvalue is None or oldvalue != value:   
                            data.append({'tag':tag,'ts':dt.strftime("%Y-%m-%d %H:%M:%S") ,'value':value})
                            oldvalue = value               
           
            except Exception as e:
                logger.warning(e)
    return(data)

def read_hist_parquet(tags,start,end,query = None):
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    engines = {}
    for k,v in cfg.items():
        engines[k] = v[DATADIR]

    #split tag to get engine and logical number
    files = {}
    data = []

    #timebased iset file indexing
    start_date = start
    end_date = end 

    year_difference = end_date.year - start_date.year
    month_difference = end_date.month - start_date.month    
    total_months = (year_difference * 12) + month_difference    
    year_month_list = [
        (start_date.year + (month // 12),f"{(month % 12) + 1:0>1X}{str(start_date.year + (month // 12))[-2:]}",(month % 12) + 1)
        for month in range(start_date.month - 1, start_date.month  + total_months)
    ]

    hist_to_force = dict()
    for elements in year_month_list:
        for tag in tags:
            try:

                engine = tag.split("::")[0]
                tag_adr = tag.split("::")[1]
                NLogico = int(tag_adr[slice(0,len(tag_adr)-6, 1)])
                type_idx = tag_adr[slice(len(tag_adr)-6, len(tag_adr)-4, 1)]
                index =  int(tag_adr[slice(len(tag_adr)-4, len(tag_adr), 1)])
                prefix = f"{engine}::{NLogico}"
            
                #time costraint
                year = elements[0]
                format = elements[1]    
                month = elements[2]          
                types =  ["IA","ID","UA","UD","SP"]
                
                #data type
                datatype = types[int(type_idx)]   

                #logical number 
                if NLogico < 10 :
                    s_nlogico = f"00{NLogico}"
                elif NLogico >= 10 and NLogico < 100:
                    s_nlogico = f"0{NLogico}" 
                else:
                    s_nlogico = f"{NLogico}"  
            
                #banco dati           
                if datatype == "IA" or datatype == "UA": 
                    offset = int((index-1)/16)
                    
                    if offset > 0 :
                        ch_number = (index-1) % (offset * 16)
                        #sqloffset = offset * 16
                    else:
                        ch_number = (index-1)
                        #sqloffset = offset

                elif  datatype == "ID" or datatype == "UD":
                    offset = int((index-1)/64)
                    if offset > 0 :
                        ch_number = (index-1) % (offset * 64)
                        #sqloffset = offset * 64
                    else:
                        ch_number = (index-1)
                        #sqloffset = offset
                #force 
                ch_number = index
                if offset < 10:
                    banco = f"B0{offset}"
                if offset >= 10:
                    banco = f"B{offset}"

                filename = f"{datatype}{s_nlogico}{banco}.{format}"

                path = f"{engines[engine]}{year}\{filename}"


                #this method call is defined by the fact of the requested month and the 
                rt_month = datetime.now().month
                rt_year = datetime.now().year
                
                
                if month == rt_month and year == rt_year:
                    hist_to_force[path] = {'engine':engine,'year':year,'month':month}                    
                
                path = f"C:/Apps/Production/Apps/iset/historian/engine={engine}/year={year}/month={month}/{filename}.parquet"
              
                '''
                new_index = ch_number+1
                if ch_number < 10 :
                    new_index = f"000{ch_number+1}" 
                elif ch_number >= 10 and ch_number < 100:
                    new_index = f"00{ch_number+1}"
                elif ch_number >= 100 and ch_number < 1000:
                    new_index = f"0{ch_number+1}"  
                
                
                if tag not in files:
                    files[tag] = list()
                '''
                if os.path.exists(path):
                    #files[tag].append({'path':path,'tag':new_index})
                    files[path]=path

            except Exception as e:
                logger.error(e)


    """
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = list()
        for path,v in hist_to_force.items():
            futures.append(executor.submit(extract_hist_numpy,v['engine'],[path],v['year'],v['month']))
            
        for future in concurrent.futures.as_completed(futures):
            result = future.result()           
            #extract_hist_numpy(v['engine'],[path],v['year'],v['month'])
    """    

    for path,v in hist_to_force.items():
        try:
            print('reading',path)
            extract_hist_numpy(v['engine'],[path],v['year'],v['month'])
        except Exception as e:
            logger.error(e)

    paths = list(files.keys())

    '''
    tag = ""
    new_index = ""
    for key, value in files.items():
        for file in value:
            tag = key
            new_index = file['tag']
            paths.append(file['path'])
    '''

    def querydb(paths,tags,query,retry = 3):
        try:
            with duckdb.connect() as conn:
                table_name = 'isethist'
                preselect = f""" (select tag, ts, value from read_parquet({paths}, union_by_name = true) where tag in {tuple(tags)} order by tag desc)"""
                fs = fsspec.filesystem('memory')
                conn.register_filesystem(fs)
                conn.sql(f"COPY {preselect} TO 'memory://{table_name}.parquet' (FORMAT 'parquet', COMPRESSION 'zstd', USE_TMP_FILE 'false')")
                if query is None :   
                            
                    conn.sql(f"select * from read_parquet('memory://{table_name}.parquet')").show()
                    with fs.open(f'{table_name}.parquet', 'rb') as file:
                        data = file.read()
                        return(data)            
                else:

                    query = query.replace("table","'memory://isethist.parquet'")
                    query= query.replace('\n','').replace(';','')

                    #query = "SELECT  tag AS tag, CAST(ts AS DATE)AS date, MIN(value) AS min, ANY_VALUE (ts) AS timestamp FROM read_parquet('memory://isethist.parquet') GROUP BY tag, date ORDER BY tag,date"            

                    conn.sql(f"COPY ({query}) TO 'memory://result.parquet' (FORMAT 'parquet', COMPRESSION 'zstd', USE_TMP_FILE 'false')")
                    conn.sql(f"select * from read_parquet('memory://result.parquet')").show()
                    with fs.open(f'result.parquet', 'rb') as file:
                        data = file.read()
                        return(data)
        except duckdb.ParserException as e:
            logger.error(e)
            return(e)
        except duckdb.ProgrammingError  as e:
            logger.error(e)
            return(e)
        except Exception as e:
            logger.error(e)
            logger.info('retrying after 1 sec .....')
            time.sleep(2)
            if retry > 0:
                newretry = retry -1
                result = querydb(paths,tags,query,newretry)
                if result is not None:
                    return result
    finalresult = querydb(paths,tags,query)
    return finalresult
    #this shouldn't be executed at all

def read_hist_chart(tags,start,end):
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    engines = {}
    for k,v in cfg.items():
        engines[k] = v[DATADIR]

    #split tag to get engine and logical number
    files = {}
    data = []

    #timebased iset file indexing
    start_date = start #datetime.strptime(start,"%d/%m/%Y %H:%M:%S")
    end_date = end #datetime.strptime(end,"%d/%m/%Y %H:%M:%S")

    year_difference = end_date.year - start_date.year
    month_difference = end_date.month - start_date.month    
    total_months = (year_difference * 12) + month_difference    
    year_month_list = [
        (start_date.year + (month // 12),f"{(month % 12) + 1:0>1X}{str(start_date.year + (month // 12))[-2:]}")
        for month in range(start_date.month - 1, start_date.month  + total_months)
    ]
    fs = fsspec.filesystem('memory')
    for elements in year_month_list:
        for tag in tags:
            try:
                engine = tag.split("::")[0]
                tag_adr = tag.split("::")[1]
                NLogico = int(tag_adr[slice(0,len(tag_adr)-6, 1)])
                type_idx = tag_adr[slice(len(tag_adr)-6, len(tag_adr)-4, 1)]
                index =  int(tag_adr[slice(len(tag_adr)-4, len(tag_adr), 1)])
                prefix = f"{engine}::{NLogico}"
            
                #time costraint
                year = elements[0]
                format = elements[1]                
                types =  ["IA","ID","UA","UD","SP"]
                
                #data type
                datatype = types[int(type_idx)]   

                #logical number 
                if NLogico < 10 :
                    s_nlogico = f"00{NLogico}"
                elif NLogico >= 10 and NLogico < 100:
                    s_nlogico = f"0{NLogico}" 
                else:
                    s_nlogico = f"{NLogico}"  
            
                #banco dati           
                if datatype == "IA" or datatype == "UA": 
                    offset = int((index-1)/16)
                    
                    if offset > 0 :
                        ch_number = (index-1) % (offset * 16)
                        sqloffset = offset * 16
                    else:
                        ch_number = (index-1)
                        sqloffset = offset

                elif  datatype == "ID" or datatype == "UD":
                    offset = int((index-1)/64)
                    if offset > 0 :
                        ch_number = (index-1) % (offset * 64)
                        sqloffset = offset * 64
                    else:
                        ch_number = (index-1)
                        sqloffset = offset
                
                if offset < 10:
                    banco = f"B0{offset}"
                if offset >= 10:
                    banco = f"B{offset}"

                filename = f"{datatype}{s_nlogico}{banco}.{format}"

                path = f"{engines[engine]}{year}\{filename}"
                

                if filename not in files.keys():
                    if datatype == "IA" or datatype == "UA": 
                        files[filename] = extract_analog(path)
                    elif  datatype == "ID" or datatype == "UD":
                        files[filename] = extract_digital(path)                        
                    time.sleep(0.001)

                for ts in files[filename]:
                    dt = datetime.fromtimestamp(ts/1000)  
                    value = files[filename][ts][ch_number]          
                    data.append({'tag':tag,'ts':dt.strftime("%Y-%m-%d %H:%M:%S") ,'value':value})  
                    #data[tag].append([ts,value])                      
                          
           
            except Exception as e:
                logger.error(e)
    
    return(data)

def extract_hist_numpy(engine, files, year, month ):

    def get_digital(digital_path,folder_path,tag_prefix,offset=0):
        # Define record structure
        dtype = np.dtype([
            ('timestamp', '<f8'),
            ('channels', np.int8, 64)
        ])
        
        # Read data blocks
        with open(digital_path, 'rb') as file:
            file.seek(72)  # Skip header
            data = np.fromfile(file, dtype=dtype)
        
        # Reshape data efficiently
        valid_mask = data['timestamp'] != 0
        filtered_data = data[valid_mask]
        timestamps = np.repeat((filtered_data['timestamp'] - 25569) * 86400, 64) #ole ts to unix ts conversion
        channels = filtered_data['channels'].flatten()
        channel_numbers = np.tile(np.arange(1,65), len(filtered_data))
        # Stack into final array
        result = np.vstack((timestamps, channel_numbers + offset, channels))

        #duckdb.sql("select to_timestamp(column0) AT TIME ZONE 'UTC' as ts, CONCAT('AIN.TNR0.23.',CAST(column1 AS INTEGER) ) as ch, column2 as value from result order by ts desc").show()
        query = f"""
                COPY (SELECT to_timestamp(column0) AT TIME ZONE 'UTC' as ts,
                      CONCAT('{tag_prefix}',LPAD(CAST(CAST(column1 AS INTEGER) AS VARCHAR),4,'0')) as tag,
                      CAST(column2 AS INTEGER) as value from result)
                TO '{folder_path}'
                (FORMAT 'parquet', COMPRESSION 'zstd')
                """
        duckdb.sql(query)
        #duckdb.sql(f"select * from read_parquet('{folder_path}')").show()

    def get_analog(analog_path,folder_path,tag_prefix,offset=0):
        # Define record structure
        dtype = np.dtype([
            ('timestamp', '<f8'),
            ('channels', '<f4', 16)
        ])
        
        # Read data blocks
        with open(analog_path, 'rb') as file:
            file.seek(72)  # Skip header
            data = np.fromfile(file, dtype=dtype)
        
        # Reshape data efficiently
        valid_mask = data['timestamp'] != 0
        filtered_data = data[valid_mask]
        timestamps = np.repeat((filtered_data['timestamp'] - 25569) * 86400, 16) #ole ts to unix ts conversion
        channels = filtered_data['channels'].flatten()
        channel_numbers = np.tile(np.arange(1,17), len(filtered_data))
        # Stack into final array
        result = np.vstack((timestamps, channel_numbers + offset, channels))

        #duckdb.sql("select to_timestamp(column0) AT TIME ZONE 'UTC' as ts, CONCAT('AIN.TNR0.23.',CAST(column1 AS INTEGER) ) as ch, column2 as value from result order by ts desc").show()
        query = f"""
                COPY (SELECT to_timestamp(column0) AT TIME ZONE 'UTC' as ts,CONCAT('{tag_prefix}',
                      LPAD(CAST(CAST(column1 AS INTEGER) AS VARCHAR),4,'0')) as tag,column2 as value from result)
                      TO '{folder_path}'
                      (FORMAT 'parquet', COMPRESSION 'zstd')
                """
        duckdb.sql(query)
        
        #duckdb.sql(f"select to_timestamp(column0) AT TIME ZONE 'UTC' as ts,CONCAT('{tag_prefix}',LPAD(CAST(CAST(column1 AS INTEGER) AS VARCHAR),4,'0')) as tag,column2 as value from result").show()
        #duckdb.sql(f"select to_timestamp(column0) AT TIME ZONE 'UTC' as ts,CONCAT('{tag_prefix}',CAST(column1 AS INTEGER) ) as ch,column2 as value from result").show()

    all_files = files 
    try:
        type_dict = {'IA':'00','ID':'01','UA':'02','UD':'03','SP':'04'}

        for path in all_files:
            if 'ALL' in path or 'zip' in path:
                continue
            try:
                parquet_name = path.replace('\\\\','\\').split('\\')[-1]
                datatype = parquet_name[:2]
                logical_n = int(parquet_name[2:5])
                month = int(parquet_name[-3],16)
                
                #year = parquet_name[-2:]
                tag_prefix = f"{engine}::{logical_n}{type_dict[datatype]}"
                #Iset tag structure
            except:
                continue

            folder_path = f"historian\engine={engine}\year={year}\month={month}"
            print(month,parquet_name[5:6],folder_path)
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
            
            if os.path.isfile(f"{folder_path}\{parquet_name}.parquet"):
                pass#continue

            if datatype == "IA" or datatype == "UA": 
                offset = (int(parquet_name[-6:-4]) * 16)
                hist_file = get_analog(path,f"{folder_path}\{parquet_name}.parquet",tag_prefix,offset)
            elif  datatype == "ID" or datatype == "UD":
                offset = (int(parquet_name[-6:-4]) * 64)
                get_digital(path,f"{folder_path}\{parquet_name}.parquet",tag_prefix,offset)  
    except Exception as e:
        logger.error(e)

async def background_loop():
    # Your code to maintain application state goes here
    nc = component(toml_config['nats'])
    await nc.connect(bucket = 'iset') 
    watcher = await nc.watchkv('iset.config.>')
    isetconfig = dict()
    while True:
        # hydrate state store 
        
        try:
            lastresult = await watcher.updates(timeout=2)
            if lastresult is not None:
                isetconfig[lastresult.key] = json.loads(lastresult.value.decode())
            '''
            configtresult = await configwatcher.updates(timeout=2)
            if lastresult is not None:
                appstate.isetconfig[configtresult.key] = json.loads(configtresult.value.decode())
            '''
        except Exception as e:
            logger.error(e)
        
        #await asyncio.sleep(1)    

def get_hist_task():
    # Get the current date
    current_date = datetime.now()
    first_day_of_current_month = current_date.replace(day=1)
    last_day_of_previous_month = first_day_of_current_month - timedelta(days=1)
    last_month_year = last_day_of_previous_month.year
    last_month_month = last_day_of_previous_month.month

    to_recover = [{'year':last_month_year,'month':last_month_month},{'year':current_date.year,'month':current_date.month}]
    print(to_recover)
    for e in to_recover:
        logger.info(f"iset hist recovery for {e['year']}/{e['month']}")
        hist_recovery(e['year'],e['month'])

def hist_recovery(year = None, this_month = None):
    start = datetime.now()

    if year is None:
        year = datetime.now().year
    if this_month is None:
        this_month = datetime.now().month  
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    for engine,v in cfg.items():          
        if engine != "SQLGLOG":
            #for year in range(2006,2024):
            engine_path = v[DATADIR]

            for month in range(1, 13):
                if month != this_month:
                    continue
                wildcard = f"{engine_path}{year}\*.{month:X}*"
                all_files = glob.glob(wildcard, recursive=True)

                for hist_file in all_files:
                    extract_hist_numpy(engine,[hist_file],year,month)
                    print(hist_file)
                    time.sleep(0.01)
    end = datetime.now()
    print('duration',end-start)
    time.sleep(60)

if __name__ == "__main__":  
    #asyncio.run(main())
    for year in range(2022,2025):
        for  month in range(1,12):
            hist_recovery(year,month)