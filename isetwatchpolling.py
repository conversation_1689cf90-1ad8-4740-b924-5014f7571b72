import sys
import struct
from configparser import Config<PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
import asyncio
import glob
import time
from messaging import component
import json
import threading 
from collections import deque
import duckdb
import fsspec
import logging
from logging.handlers import RotatingFileHandler
import os
from config import app_config

toml_config = app_config('config.toml')
"""
this app watches for changes in the last files from iset plantsx extract the binary data and sends a parquet file to nats "iset bucket on keys partitioned as follows:
    "iset.last.{engine}.{NLogico}.{type}.{index}"", these parquet files can be consumed by all services, especialy webscada
"""

# create log folder
if not os.path.isdir("log"):
    os.makedirs('log')

#logger
logger = logging.getLogger('isetwatch')
logger.setLevel(logging.INFO)

# Create a rotating file handler
log_file = f"./log/isetwatch.log"
handler = RotatingFileHandler(log_file, maxBytes=1024*1024*50, backupCount=2)
formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
handler.setFormatter(formatter)

# Console handler
console_handler = logging.StreamHandler()
console_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(handler)
logger.addHandler(console_handler)


CONFIGDIR = 'dirconfig'
DATADIR = 'dirdati'
LASTDIR = 'dirlast'
OLE_TIME_ZERO = datetime(1899, 12, 30, 0, 0, 0)

lastvalues = dict()
de = deque()
fs = fsspec.filesystem('memory')

def tag_to_topic(tag):
    try:
        engine = tag.split("::")[0]
        tag_adr = tag.split("::")[1]

        NLogico = int(tag_adr[slice(0,len(tag_adr)-6, 1)])
        types =  ["AIN","DIN","AOUT","DOUT","SP"]
        type_idx = int(tag_adr[slice(len(tag_adr)-6, len(tag_adr)-4, 1)])
        type = types[type_idx]
        index =  int(tag_adr[slice(len(tag_adr)-4, len(tag_adr), 1)])

        topic = f"iset.last.{engine}.{NLogico}.{type}.{index}"
    except:
        topic = ""
    
    return topic

def Ole2PosixTS(oledt):
    """
    this method converts Ole timestamp to an epoch timestamp
    """    
    if oledt == 0:
        return 0
    tz_offset = datetime.now().astimezone().utcoffset()     
    oletime = OLE_TIME_ZERO + timedelta(days=oledt) #+ tz_offset    
    #return oletime.strftime('%c')    
    return oletime.timestamp() * 1000

def get_config(path):
    Engines = {}
    try:
        configure = ConfigParser()
        iset_server_config = configure.read(path)
        for key in configure.sections():
                Engines[key] = {}
                for values in configure[key]:
                    Engines[key][values]= configure[key][values]
        Engines.pop('Sezioni')                    
    except Exception as e:
        logger.error(e)
    return Engines

def decode_last_raw(path,prefix)-> dict:
    data = dict()
    with open(path,'rb') as f:
        f.read(4)

        def index(i):
            index = f"000{i}"
            if i >= 10:
              index = f"00{i}"
            if i >= 100:
                index = f"0{i}"
            if i >= 1000:
                index = f"{i}"
            return index    
        
        #read analog inputs
        for i in range(1,257):
            mode = {0:'regolare',1:'allarme superiore',2:'allarme inferiore',9:'anomalia'}
            key = f'{prefix}00{index(i)}'
            key = f'{prefix}.AIN.{i}'
            state = int.from_bytes(f.read(4), "little")
            value = round(struct.unpack('f', f.read(4))[0],2)
            data[key] = {'state': mode.get(state,'unknown'), 'value' :value}

        #read digital inputs
        for i in range(1,1025):            
            key = f'{prefix}01{index(i)}'
            key = f'{prefix}.DIN.{i}'
            state = int.from_bytes(f.read(4), "little")
            value = int.from_bytes(f.read(4), "little") 
            data[key] = {'state': state, 'value' :value}

        #read digital outputs
        for i in range(1,1025):
            mode = {0:'auto',1:'man'}
            key = f'{prefix}03{index(i)}'
            key = f'{prefix}.DOUT.{i}'
            state = int.from_bytes(f.read(4), "little")
            value = int.from_bytes(f.read(4), "little")
            data[key] = {'state': mode.get(state,'unknown'), 'value' :value}  
       
        #read analog outputs
        for i in range(1,257):
            mode = {0:'auto',1:'man'}
            key = f'{prefix}02{index(i)}'
            key = f'{prefix}.AOUT.{i}'
            state = int.from_bytes(f.read(4), "little")
            value = round(struct.unpack('f', f.read(4))[0] ,2)
            data[key] = {'state': mode.get(state,'unknown'), 'value' :value}        
        return data          

async def main(path,index):
    logger.info(f"{path}, started")
    engines = dict()
    engines[path] = glob.glob(f'{path}\last*.*', recursive=True)
    nc = component(toml_config['nats'])
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)

    await nc.connect( bucket = 'iset')
    """
    #iset_kv = await nc.key_value('iset')
    keyslist = await nc.keys()
    for key in keyslist:
        
        if '@' in key:
            print(key)
            response = await nc.delete(key)
            print(response)
    await nc.close()
    iset_keys = [element for element in keyslist if '.config.'  in element]
    """
    old_ts = dict()
    while True:
        try:
            for path,files in engines.items():                 
                #initial load of last files                
                for file in files:
                    if  'zip' in file:
                        continue
                    
                    tree = file.split('\\')
                    file_ts = os.path.getmtime(file)
                    if (file in old_ts ):
                        if old_ts[file] == file_ts:                            
                            continue
                        else:
                            print('modificato',file)
                    #old_ts[file] = file_ts    
                    async def upload(retry = 5):
                        try:                      
                            engine = tree[-3]
                            logical_number = tree[-1].split('.')[0].lower().replace('last','')
                            prefix = f"iset.last.{engine}.{logical_number}"
                            #topic = f"iset.last.{engine}.{logical_number}"                                           
                            data = decode_last_raw(file,prefix) 
                            #de.append((prefix,data))
                         
                            templist = []
                            for key,value in data.items():
                                templist.append({'tag':key,'ts':file_ts, **value})
                            
                            with duckdb.connect() as conn:
                                # Create a memory filesystem and write the dictionary data to it                            
                                with fs.open(f'{index}.json', 'w') as memfile:
                                    memfile.write(json.dumps(templist))

                                # Register the memory filesystem and create the table
                                conn.register_filesystem(fs)
                                conn.execute(f"CREATE TABLE IF NOT EXISTS {index} AS SELECT * FROM read_json_auto('memory://{index}.json')")
                                result = conn.sql(f'select * from {index}').fetchmany(10)
                                if len(result) < 10:
                                    raise Exception('Empty dict')
                                    
                                conn.sql(f'select * from {index}').show()
                                conn.sql(f"COPY {index}  TO 'memory://{index}.parquet' (FORMAT 'parquet', CODEC 'zstd',USE_TMP_FILE 'false')")
                                with fs.open(f'{index}.parquet', 'rb') as f:
                                    #last = file.read()
                                    await nc.put(prefix,f.read())
                                    """
                                    with open(f'{prefix}.parquet','wb') as f:
                                        f.write(file.read())
                                    """    
                            old_ts[file] = file_ts  


                            """
                            for tag,val in data.items(): 
                                if tag.replace('last','config') in iset_keys:
                                    val['ts'] = file_ts
                                    #await nc.put(tag,json.dumps(val).encode())
                                    lastvalues[tag]= val
                                    #print(tag)
                                    #await asyncio.sleep(0.001)
                            """                        
                        except Exception as e:
                            logger.error(e)
                            if(retry > 0):
                                logger.info(f"retrying to upload, {file}, {retry}")                                
                                retry -= 1
                                await upload(retry)
                                await asyncio.sleep(1)

                    await upload()
                    #await asyncio.sleep(0.001)

            #print(datetime.now(), 'Initial load done........')
            
        except Exception as e:
            logger.error(e) 
        '''
        finally:
            #print('restarting...')  
        '''     
        await asyncio.sleep(1)
        

def async_wrapper(path,engine):
    logger.info(engine)
    asyncio.run(main(path,engine))

def isetwatch():
    logger.info('iset last watcher started')
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    thread_list = list()
    for k,v in cfg.items():
        if k != "SQLGLOG":#SQLGLOG                              
            path = str(v[LASTDIR])       
            thread_list.append(threading.Thread(name=k,target=async_wrapper,args=[path,k]))
    for thread in thread_list:
        thread.start()
    
    while True:
        """
        idle
        """
        time.sleep(5)

if __name__ == "__main__":
    isetwatch()