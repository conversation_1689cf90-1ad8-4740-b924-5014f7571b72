import tomllib
import os
import sys

def app_config(config_path=None):
    if config_path is None:
        # Look for config.toml in the same directory as the executable
        if getattr(sys, 'frozen', False):
            # Running as compiled executable
            exe_dir = os.path.dirname(sys.executable)
            config_path = os.path.join(exe_dir, "config.toml")
        else:
            # Running as script
            config_path = "config.toml"
    
    try:
        with open(config_path, "rb") as f:
            config = tomllib.load(f)
            return config
    except FileNotFoundError:
        print(f"Config file not found: {config_path}")
        print("Please ensure config.toml is in the same directory as the executable")
    except tomllib.TOMLDecodeError as e:
        print(f"Invalid TOML format in {config_path}: {e}")

