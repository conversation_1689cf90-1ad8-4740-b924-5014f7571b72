import json
import hashlib
import io
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from xml.etree import cElementTree as ElementTree
from passlib.hash import pbkdf2_sha256

class CryptoServices:
    ALGORITHM_KEY = bytes([
        123, 246, 223, 94, 91, 189, 57, 255, 110, 169, 99, 158, 18, 227, 253, 138,
        168, 88, 146, 30, 199, 240, 110, 123, 126, 20, 178, 44, 204, 145, 210, 216
    ])
    ALGORITHM_IV = bytes([
        84, 6, 97, 11, 197, 199, 176, 129, 131, 234, 161, 181, 43, 45, 27, 199
    ])

    @staticmethod
    def get_algorithm():
        return AES.new(CryptoServices.ALGORITHM_KEY, AES.MODE_CBC, CryptoServices.ALGORITHM_IV)

    @staticmethod
    def generate_params():
        cipher = AES.new(CryptoServices.ALGORITHM_KEY, AES.MODE_CBC)
        return cipher.key, cipher.iv

    def __init__(self):
        self.m_encryptor = self.get_algorithm()
        self.m_decryptor = self.get_algorithm()

    def encrypt_string(self, plaintext, encoding='ascii'):
        return self.encrypt(plaintext.encode(encoding))

    def encrypt(self, plaindata):
        cipher = self.get_algorithm()
        encrypted = cipher.encrypt(pad(plaindata, AES.block_size))
        return encrypted

    def create_encrypt_stream(self, stream, read):
        mode = AES.MODE_CBC
        return io.BytesIO(self.encrypt(stream.read()) if read else stream)

    def decrypt_string(self, cipherdata, encoding='ascii'):
        decrypted = self.decrypt(cipherdata)
        return decrypted.decode(encoding).rstrip('\x00')

    def decrypt(self, cipherdata):
        cipher = self.get_algorithm()
        decrypted = unpad(cipher.decrypt(cipherdata), AES.block_size)
        return decrypted

    def create_decrypt_stream(self, stream, read):
        mode = AES.MODE_CBC
        return io.BytesIO(self.decrypt(stream.read()) if read else stream)

    def compute_hash(self, content):
        md5 = hashlib.md5()
        md5.update(content)
        return md5.digest()

def get_users(filename):
#filename = "users.data"
    with open(filename, 'rb') as file:
        binaryfile =  file.read()

    xmlstring = CryptoServices().decrypt_string(binaryfile, 'ascii')  
    root = ElementTree.XML(xmlstring)

    userlist = list ()
    roles = {'VAL':'Validazione','OPR':'Operatività','USR':'UTENTI','SRV':'Server','LAY':'Accessibilità'}
    grants = {'Operatività':['Modifica configurazioni']}
    for users in root[0]:
        localdict = dict()
        for k,v in users.attrib.items():        
            localdict[k] = v
            localdict['email'] = ""

        localdict['profile'] = {}
        for profile in users[0]:
            if 'name' in profile.attrib:
                name = profile.attrib['name']
                localdict['profile'][name] = profile.attrib 
            else:
                #localdict = profile.attrib   
                continue
            for group in profile:
                if 'group' not in localdict['profile'][name]:
                    localdict['profile'][name]['group'] = []
                
                if 'right' in group.attrib:
                    user_grant = group.attrib['right']
                    for i in range((10 - len(user_grant))):
                        user_grant += '0'
                    group.attrib['right'] = int(user_grant,base=2)

                localdict['profile'][name]['group'].append(group.attrib)
                #print(group.attrib)
        userlist.append(localdict)
    return userlist