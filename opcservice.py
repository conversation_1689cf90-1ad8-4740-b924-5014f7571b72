import asyncio
import json
import re
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime, timezone
from typing import Dict, List, Optional
from messaging import component
import glob
from asyncua import Client, ua, Node
from asyncua.common.events import Event
from asyncua.common.subscription import DataChangeNotif
import nats.errors
import nats.js.errors
import uuid
import os
from config import app_config

toml_config = app_config('config.toml')
# create log folder
if not os.path.isdir("log"):
    os.makedirs('log')

#logger
logger = logging.getLogger('opcservice')
logger.setLevel(logging.INFO)

# Create a rotating file handler
log_file = f"./log/opcservice.log"
handler = RotatingFileHandler(log_file, maxBytes=1024*1024*50, backupCount=2)
formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
handler.setFormatter(formatter)

# Console handler
console_handler = logging.StreamHandler()
console_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(handler)
logger.addHandler(console_handler)

class SubscriptionHandler:
    def __init__(self, nc):
        self.nc = nc
        super().__init__()

    def makeDictFromDataValue(dv: ua.DataValue):
        '''
        makes a simple dict from DataValue-Class 
        ! only for opc ua built in types !
        '''
        return {
            "Value": str(dv.Value.Value),
            "ArrayDimensions": str(dv.Value.Dimensions),
            "VariantType":str(dv.Value.VariantType.name),
            "Status": str(dv.StatusCode.name),
            "SourceTimestamp": str(dv.SourceTimestamp.replace(tzinfo=timezone.utc).timestamp()) if dv.SourceTimestamp else None,
            "ServerTimestamp": str(dv.ServerTimestamp.replace(tzinfo=timezone.utc).timestamp()) if dv.ServerTimestamp else None,
        }
    
    async def datachange_notification(self, node, val, data):
        """Handle data change notifications from OPC UA"""
        try:
            topic = f"opcua.{node.nodeid.to_string()}"
            payload= self.makeDictFromDataValue(
                    data.monitored_item.Value
                    )
                
            await self.nc.pub(
                topic,
                json.dumps(payload).encode()
            )        
        except Exception as e:
            logger.error(f"Error handling datachange notification: {e}")

class OpcNatsService:
    def __init__(self, opc_url: str):
        self.opc_url = opc_url
        self.opc_client = None
        self.nc = component(toml_config['nats'])
        self.subscriptions: Dict[str, Dict] = {}
        self.kv_store = None
        self._connection_monitor_task = None
        self._is_connected = False
        self._subscription_handler = None
        self._iset_config = dict()


    async def _get_iset_config(self):
        ini_files = glob.glob(r"\\*************\c\motori_OPC\*\OpcClient.ini")
        ini_dict = {}
        for file in ini_files:
            with open(file, "r") as f:
                ini_content = f.read()
            row = {}
            lines = ini_content.strip().splitlines()
            for line in lines:
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    if key == 'numero_stazione' or key == 'opc_suffisso':
                        row[key] = value.strip()
            ini_dict[row['numero_stazione']] = row['opc_suffisso']

        configwatcher = await self.nc.watchkv('iset.config.TNP.>') 
        while True:
            try:
                #watching the config
                isetkv = await configwatcher.updates(timeout=2)
                if isetkv is not None:
                    if 'info' not in isetkv.key:
                        logical_n =  isetkv.key.split('.')[3]
                        data = json.loads(isetkv.value.decode())        
                        if ('Tag' in data):
                            prefix = ini_dict.get(logical_n,None)
                            if prefix is not None:                            
                                self._iset_config[data['Tag']] = f"ns=2;s={prefix}{data['ExpAddress']}"

            except nats.errors.TimeoutError as e:
                #logger.info('initial config loaded')
                pass
            except Exception as e:
                print('config loop error',e)
                
    async def connect(self):
        """Establish connections to both OPC UA and NATS servers with backoff"""
        await self._connect_nats()
        await self._connect_opc()
        asyncio.create_task(self._get_iset_config())
        
    async def _connect_nats(self):
        """Connect to NATS server with backoff"""
        retry_count = 0
        max_retries = 5
        base_delay = 1
        
        while retry_count < max_retries:
            try:
                await self.nc.connect(bucket = 'iset')
                logger.info(f"Connected to NATS server ")
                return
            except Exception as e:
                delay = base_delay * (2 ** retry_count)
                logger.error(f"Failed to connect to NATS: {e}. Retrying in {delay} seconds...")
                await asyncio.sleep(delay)
                retry_count += 1
                
        raise Exception("Failed to connect to NATS server after maximum retries")
        
    async def _connect_opc(self):
        """Connect to OPC UA server with backoff"""
        retry_count = 0
        max_retries = 50
        base_delay = 1
        
        while retry_count < max_retries:
            try:
                self.opc_client = Client(url=self.opc_url)
                await self.opc_client.connect()
                self._is_connected = True
                logger.info(f"Connected to OPC UA server at {self.opc_url}")
                return
            except Exception as e:
                delay = base_delay * (2 ** retry_count)
                logger.error(f"Failed to connect to OPC UA: {e}. Retrying in {delay} seconds...")
                await asyncio.sleep(delay)
                retry_count += 1
                
        raise Exception("Failed to connect to OPC UA server after maximum retries")

    async def _monitor_connection(self):
        """Monitor OPC UA connection and handle reconnection"""
        while True:
            try:
                if not self._is_connected:
                    logger.warning("OPC UA connection lost, attempting to reconnect...")
                    await self._connect_opc()
                else:
                    # Try to read a simple node to check connection
                    try:
                        root = self.opc_client.get_root_node()
                        await root.read_node_class()
                    except Exception as e:
                        logger.error(f"OPC UA connection check failed: {e}")
                        self._is_connected = False
                        if self.opc_client:
                            await self.opc_client.disconnect()
                            self.opc_client = None
            except Exception as e:
                logger.error(f"Error in connection monitor: {e}")
                self._is_connected = False
                
            await asyncio.sleep(10)  # Check connection every 5 seconds

    async def _ensure_connection(self):
        """Ensure OPC UA connection is active before performing operations"""
        if not self._is_connected:
            await self._connect_opc()
        return self._is_connected

    def _sanitize_nodename(self, nodename: str) -> str:
        """Sanitize node name for NATS topic"""
        return re.sub(r'[\s$]', '_', nodename)

    def getnodes(self):
        nodes_to_subscribe = []
        try:
            with open("tags.txt","r") as text:
                for line in text:
                    #taglist[f"{line.strip()}?AlarmHigh"] = ""
                    #taglist[f"{line.strip()}?AlarmLow"] = ""
                    #taglist[f"{line.strip()}?Value"] = ""
                    nodes_to_subscribe.append(f"{line.strip()}?Value")
                    #taglist[f"{line.strip()}?ModeString"] = "" 
                    #taglist[f"{line.strip()}?StateString"] = ""            

        except Exception as e:
            print(e)
        return nodes_to_subscribe

    async def handle_subscription_request(self, msg):
        """Handle subscription requests from NATS"""
        try:
            if not await self._ensure_connection():
                await msg.respond(json.dumps({
                    'status': 'error',
                    'message': 'OPC UA connection is not available'
                }).encode())
                return

            data = {}#json.loads(msg.data.decode())
            subscription_id = data.get('subscription_id',str(uuid.uuid4()))
            nodes = data.get('nodes', self.getnodes())
            interval = data.get('interval', 1000)  # Default 1 second
            
            if not subscription_id or not nodes:
                await msg.respond(json.dumps({
                    'status': 'error',
                    'message': 'Missing subscription_id or nodes'
                }).encode())
                return
            
            nodes_to_subscribe = []
            for node in nodes:
                nodes_to_subscribe.append(self.opc_client.get_node(node))

            # Create subscription handler if not exists
            if not self._subscription_handler:
                self._subscription_handler = SubscriptionHandler(
                    self.nc
                )
                
            # Create subscription
            subscription = await self.opc_client.create_subscription(
                period=interval,
                handler=self._subscription_handler,
                publishing=True
            )
            
            # Store subscription details
            self.subscriptions[subscription_id] = {
                'nodes': nodes_to_subscribe,
                'interval': interval,
                'subscription': subscription,
                'handles': []
            }
            # Subscribe to nodes
            try:
                node_handles = await subscription.subscribe_data_change(
                        nodes=nodes_to_subscribe, 
                        attr=ua.AttributeIds.Value, 
                        queuesize=100, 
                        monitoring=ua.MonitoringMode.Reporting
                    )
                self.subscriptions[subscription_id]['handles'].append(node_handles)
            except Exception as e:
                logger.error(f"Error subscribing to node {node}: {e}")
                # Clean up subscription on error
                await self._cleanup_subscription(subscription_id)
                raise          
            """
            for node in nodes:
                try:
                    node_obj = self.opc_client.get_node(node)
                    handle = await subscription.subscribe_data_change(node_obj)
                    self.subscriptions[subscription_id]['handles'].append(handle)
                except Exception as e:
                    logger.error(f"Error subscribing to node {node}: {e}")
                    # Clean up subscription on error
                    await self._cleanup_subscription(subscription_id)
                    raise
            """
            # Subscribe to events TODO

            await msg.respond(json.dumps({
                'status': 'success',
                'message': f'Subscription {subscription_id} created'
            }).encode())
            
        except Exception as e:
            logger.error(f"Error handling subscription request: {e}")
            await msg.respond(json.dumps({
                'status': 'error',
                'message': str(e)
            }).encode())

    async def _cleanup_subscription(self, subscription_id: str):
        """Clean up subscription resources"""
        if subscription_id in self.subscriptions:
            sub_info = self.subscriptions[subscription_id]
            if 'subscription' in sub_info:
                try:
                    await sub_info['subscription'].delete()
                except Exception as e:
                    logger.error(f"Error deleting subscription {subscription_id}: {e}")
            del self.subscriptions[subscription_id]

    async def _subscription_task(self, subscription_id: str):
        """Background task to handle OPC UA subscriptions"""
        sub_info = self.subscriptions[subscription_id]
        nodes = sub_info['nodes']
        interval = sub_info['interval'] / 1000  # Convert to seconds
        
        while True:
            try:
                if not await self._ensure_connection():
                    logger.error("Cannot read nodes: OPC UA connection is not available")
                    await asyncio.sleep(interval)
                    continue

                for node in nodes:
                    try:
                        node_obj = self.opc_client.get_node(node)
                        value = await node_obj.read_value()
                        source_ts = datetime.utcnow().isoformat()
                        
                        # Get server timestamp if available
                        try:
                            data_value = await node_obj.read_data_value()
                            server_ts = data_value.ServerTimestamp.isoformat()
                        except:
                            server_ts = source_ts
                            
                        # Get quality if available
                        try:
                            data_value = await node_obj.read_data_value()
                            quality = data_value.Quality
                        except:
                            quality = "GOOD"
                            
                        payload = {
                            'value': value,
                            'server_timestamp': server_ts,
                            'source_timestamp': source_ts,
                            'quality': str(quality)
                        }
                        
                        # Store in KV bucket
                        sanitized_node = self._sanitize_nodename(node)
                        await self.nc.pub(
                            f"opcua.{sanitized_node}",
                            json.dumps(payload).encode()
                        )
                        
                    except Exception as e:
                        logger.error(f"Error reading node {node}: {e}")
                        self._is_connected = False
                        
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in subscription task {subscription_id}: {e}")
                self._is_connected = False
                await asyncio.sleep(interval)
                
    async def handle_write_request(self, msg):
        """Handle write requests from NATS"""
        try:
            if not await self._ensure_connection():
                await msg.respond(json.dumps({
                    'status': 'error',
                    'message': 'OPC UA connection is not available'
                }).encode())
                return

            data = json.loads(msg.data.decode())
            tag =  data.get('tag')
            node = self._iset_config.get(tag)
            value = data.get('value')
            result = json.dumps({
                    'status': 'error',
                    'node':tag,
                    'tag':node,
                    'message': 'Missing node or value'
                })
            if not node or value is None:
                await msg.respond(result.encode())
                logger.warning(result)
                return
                
            # Write to OPC UA
            node_obj = self.opc_client.get_node(node)
            read_value = await node_obj.read_value()
            #check if value could be boolean 0/1

            is_boolean = False
            try:
                dv = ua.DataValue(ua.Variant(bool(value), ua.VariantType.Boolean))
                await node_obj.set_value(dv)
                is_boolean = True
            except Exception as e:
                pass
            
            if not is_boolean:
                try:
                    dv = ua.DataValue(ua.Variant(float(value), ua.VariantType.Float))
                    await node_obj.set_value(dv)
                except Exception as e:
                    dv = ua.DataValue(ua.Variant(int(value), ua.VariantType.Int16))
                    await node_obj.set_value(dv)
            
            # Read back the value
            read_value = await node_obj.read_value()
            
            #check if value could be boolean 0/1


            await msg.respond(json.dumps({
                'status': 'success',
                'message': 'Write successful',
                'value': read_value,
                'tage' : tag
            }).encode())
            logger.info(f"Write successful tag:{tag},'node':{node},'value':{read_value}")

        except Exception as e:
            logger.error(f"Error handling write request: {e}")
            self._is_connected = False
            await msg.respond(json.dumps({
                'status': 'error',
                'node':tag,
                'tag':node,
                'message': str(e)
            }).encode())
            
    async def start(self):
        """Start the service"""
        await self.connect()
        
        # Start connection monitor
        self._connection_monitor_task = asyncio.create_task(self._monitor_connection())
        
        # Subscribe to subscription requests
        await self.nc.sub("opcua.subscribe", cb=self.handle_subscription_request)
        
        # Subscribe to write requests
        await self.nc.sub("opcua.write", cb=self.handle_write_request)
        
        logger.info("OPC NATS service started")
        
        # Keep the service running
        while True:
            await asyncio.sleep(1)
            
    async def stop(self):
        """Stop the service"""
        if self._connection_monitor_task:
            self._connection_monitor_task.cancel()
            try:
                await self._connection_monitor_task
            except asyncio.CancelledError:
                pass

        # Cancel all subscription tasks
        for sub_id, sub_info in self.subscriptions.items():
            if sub_info['task']:
                sub_info['task'].cancel()
                try:
                    await sub_info['task']
                except asyncio.CancelledError:
                    pass


        if self.opc_client:
            await self.opc_client.disconnect()
        logger.info("OPC NATS service stopped")
        if self.nc:
            await self.nc.close()        

async def opc_loop():
    opc_url = "opc.tcp://*************:49320"
    service = OpcNatsService(opc_url)    
    try:
        await service.start()
    except KeyboardInterrupt:
        await service.stop()
    except Exception as e:
        logger.error(f"Service error: {e}")
        await service.stop()

if __name__ == "__main__":
    asyncio.run(opc_loop()) 
