from configparser import ConfigParser
import asyncio
import struct 
import re
from datetime import datetime, timedelta, date
import os
import duckdb
import fsspec
import json
from messaging import component
import nats.errors
from config import app_config

toml_config = app_config('config.toml')

"""
this app reads the iset alarms files and publish them to nats kv store "alarms", the keys are partitioned by year and month "iset.alarms.{year}.{month}" in parquet format to be queried 
by diffrent consumers for streaming query engines and bigdata analytics tools, to force older alarms recovery you need to adjust the recovery timespan.
"""
OLE_TIME_ZERO = datetime(1899, 12, 30, 0, 0, 0)
SPKDIR = 'dirspk'
DATADIR = 'dirdati'
fs = fsspec.filesystem('memory')

def Ole2PosixTS(oledt):
    """
    this method converts Ole timestamp to an epoch timestamp
    """    
    if oledt == 0:
        return datetime.min
    tz_offset = datetime.now().astimezone().utcoffset()     
    oletime = OLE_TIME_ZERO + timedelta(days=oledt) #+ tz_offset    
    #return oletime.strftime('%c')    
    return oletime #oletime.timestamp() 

def get_config(path):
    Engines = {}
    try:
        configure = ConfigParser()
        iset_server_config = configure.read(path)
        for key in configure.sections():
                Engines[key] = {}
                for values in configure[key]:
                    Engines[key][values]= configure[key][values]
        Engines.pop('Sezioni')                    
    except Exception as e:
        print('Server configuration loading error: ',e)
    return Engines

def json_serial(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, (datetime, date)):
        return obj.strftime("%Y-%m-%d %H:%M:%S")
    raise TypeError ("Type %s not serializable" % type(obj))

def extract_alarm(engine,path): 
     """
     Alarm file extraction method 
     """   
     print('reading',path)
     alarms = {}  
     if os.path.exists(path) == 0:
        return alarms
     try:
        with open(path,'rb') as file:
            while True:
                #records reading
                try:                    
                    #file.read(13)
                    id = f'{engine}::{int.from_bytes(file.read(4), "little")}'
                    file.read(264)
                    timestamp = Ole2PosixTS(struct.unpack('d', file.read(8))[0])
                    file.read(13)
                    description = re.sub(r'[\x00-\x1f]', '', file.read(25).decode()).strip()
                    level = int.from_bytes(file.read(1), "little")
                    file.read(7)

                    if timestamp >=  (datetime.now() - timedelta(days = 60)):
                        #print(timestamp,id,level,description)
                        alarms[json_serial(timestamp)] = {
                            'engine': engine,
                            'tag' : id,
                            'timestamp' : timestamp,
                            'description' : description,                       
                            'level' : level
                            }

                except Exception as e:
                    #print(engine,e)  
                    return alarms 
                          
     except:
         pass
         
     return alarms 

def alarm_archive(engine,path): 
     """
     Alarm file extraction method 
     """     
     print(path)
     archive = dict()
     DESCRIPTION_LENGTH = 26
     if os.path.getsize(path) == 0:
        print("file is empty")
        return
     try:
        with open(path,"rb") as file: 
            i = 0
            #Header  reading
            '''
            VerMaj = int.from_bytes(file.read(1), "little")
            VerMin =  int.from_bytes(file.read(1), "little")
            LastRec = struct.unpack('<I', file.read(4))[0]#int.from_bytes(file.read(4), "big")
            LastRec_date =datetime.fromtimestamp(LastRec).strftime('%c')
            RecLen = int.from_bytes(file.read(2), "little")
            PosData = int.from_bytes(file.read(1), "little")
            LenData = int.from_bytes(file.read(1), "little")
            Status = int.from_bytes(file.read(1), "little")
            name = file.read(67).decode("utf-8") 
            '''
            file.read(78)
            while True:
                #records reading
                try:
                    timestamp = Ole2PosixTS(struct.unpack('d', file.read(8))[0])
                except:
                    return archive
                    break
                if timestamp == 0:
                    return archive
                    break
                
                try:
                    id = int.from_bytes(file.read(4), "little")
                    device = int.from_bytes(file.read(4), "little")
                    alarmtype = int.from_bytes(file.read(4), "little")
                    description = re.sub(r'[\x00-\x1f]', '', file.read(DESCRIPTION_LENGTH).decode())
                    severity = int.from_bytes(file.read(4), "little")
                    ackid = int.from_bytes(file.read(4), "little")
                    source = int.from_bytes(file.read(4), "little")
                    resumets = Ole2PosixTS(struct.unpack('d', file.read(8))[0])
                    ackts =   Ole2PosixTS(struct.unpack('d', file.read(8))[0])     
                    recnum = int.from_bytes(file.read(4), "little") 
                    id = f"{engine}::{id}"          
                except Exception as e:
                    print(engine,e) 
                                
                archive[i] = {
                            "timestamp" : timestamp,
                            "livello" : severity,
                            "tag" : id,
                            "messaggio" : description.strip(),
                            "tipo_allarme" : alarmtype,                    
                            "ack_timestamp" : ackts,
                            "ackid" : ackid,
                            "source" : source,
                            "record_number" : recnum                    
                    }
                i += 1
     except:
         return archive
     return archive

class Appstate:
    pass

async def main(pathindex):
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    state = Appstate()
    state.nc = component(toml_config['nats'])

    async def connect():
        try:
            print('connectiong to nats...')
            await state.nc.connect(bucket = 'iset')             
            state.alarmskv = await state.nc.key_value(bucket = 'alarms')
            state.configwatcher = await state.nc.watchkv('iset.config.>')            
        except Exception as e:
            await asyncio.sleep(1)
            print("reconnecting....",e)
            await connect()
    await connect()
    isetconfig = dict()
    loaded = False
    while not loaded:   
        try:
            ''''''
            isetkv = await state.configwatcher.updates(timeout=2)
            if isetkv is not None:
                data = json.loads(isetkv.value.decode())         
                if ('Tag' in data):
                    isetconfig[data['Tag']] = data
                else:
                     isetconfig[isetkv.key] = data
        except nats.errors.TimeoutError as e:
            loaded = True
            pass
        except Exception as e:
            print('config loop error',e)

    for data in pathindex:
        print(data)
        alarms = list()
        archive = list()
        today = datetime.today()
        
        yearsliced = today.strftime('%y')
        month = data[1][0:1]
        year = data[0]
        yearsliced = data[1][-2:]
        # hydrate config store    

        if loaded :
            for engine,path in cfg.items():
                try:
                    '''
                    result = extract_alarm(engine,f"{path[SPKDIR]}Allarmi.dat")
                    for _,record in result.items() :
                        if record['tag'] in isetconfig:
                            record['tag'] = isetconfig[record['tag']]['Nome']
                        alarms.append(record)
                    '''
                    archive_path = f"{path[DATADIR]}{year}\\ALL_BIN.{month}{yearsliced}"
                    print(archive_path)
                    archive_result = alarm_archive(engine,archive_path)
                    
                    for _,record in archive_result.items():
                        record['periferica'] = engine
                        record['address'] = record['tag']
                        if record['tag'] in isetconfig:                            
                            record['periferica'] = isetconfig[record['tag']]['Plant']
                            record['tag'] = isetconfig[record['tag']]['Nome']

                        alarms.append(record)
                        
                except:
                    pass
            try:
                with duckdb.connect() as conn:
                    # Create a memory filesystem and write the dictionary data to it
                    
                    table_name = 'rtalarms'

                    with fs.open(f'{table_name}.json', 'w') as file:
                        file.write(json.dumps(alarms,default=json_serial))

                    # Register the memory filesystem and create the table
                    conn.register_filesystem(fs)
                    conn.execute(f"CREATE TABLE IF NOT EXISTS {table_name} AS SELECT * FROM read_json_auto('memory://{table_name}.json')")
                    conn.sql(f"COPY {table_name}  TO 'memory://{table_name}.parquet'  (FORMAT 'parquet', CODEC 'zstd', USE_TMP_FILE 'false') ")
                    conn.sql(f"select * from read_parquet('memory://{table_name}.parquet') order by timestamp desc").show()
                    with fs.open(f'{table_name}.parquet', 'rb') as file:
                        alarms = file.read()
                        await state.alarmskv.put(f'iset.alarms.{year}.{int(month,16)}',alarms)
                        print(f'iset.alarms.{year}.{int(month,16)}')
                    #fs.rm(f'{table_name}.parquet')                                          
            except Exception as e:
                print(e)
                
        #await asyncio.sleep(1)

def isetindex(start: str, end: str):
    start_date = datetime.strptime(start,"%Y-%m-%d")
    end_date = datetime.strptime(end,"%Y-%m-%d")

    year_difference = end_date.year - start_date.year
    month_difference = end_date.month - start_date.month    
    total_months = (year_difference * 12) + month_difference    
    year_month_list = [
        (start_date.year + (month // 12),f"{(month % 12) + 1:0>1X}{str(start_date.year + (month // 12))[-2:]}")
        for month in range(start_date.month - 1, start_date.month  + total_months)
    ]
    return year_month_list

#
print('forzatura allarmi')
start = input('Data inizio es: "2024-01-01":\n')
end = input('Data fine es: "2024-12-31:"\n')
print(start,end)
data = isetindex(start,end)
print(data)
asyncio.run(main(data))



