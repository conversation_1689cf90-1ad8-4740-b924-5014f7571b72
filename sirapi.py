import asyncio
from datetime import datetime
from messagingsir import component
from iset import read_hist_parquet
import json
import duckdb
import fsspec
import nats.errors
from datetime import datetime
import schedule
import logging
from logging.handlers import RotatingFileHandler
import os

# create log folder
if not os.path.isdir("log"):
    os.makedirs('log')
    
#logger
logger = logging.getLogger('sirapi')
logger.setLevel(logging.INFO)

# Create a rotating file handler
log_file = f"./log/sirapi.log"
handler = RotatingFileHandler(log_file, maxBytes=1024*1024*50, backupCount=2)
formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
handler.setFormatter(formatter)

# Console handler
console_handler = logging.StreamHandler()
console_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(handler)
logger.addHandler(console_handler)

#Class Appstate
class Appstate():
    pass
#app state object
appstate = Appstate()

appstate.nc = component()
appstate.iset = dict()
appstate.fs = fsspec.filesystem('memory')  

def iset_map():
    result = list()
    CH_MODE = {"0":"Fuori Scansione", "1":"In Scansione","2": "Scansione e limiti","3": "Trasmiss. evento",     
                "4":"Allarme", "5":"Allarme inferiore","6":"Allarme superiore","7":"Non Valido","8":"Canale esportato",
                "17":"Importato Modo 1", "18":"Importato Modo 2","19":"Importato Modo 3",
                "20":"Importato Modo 4","21":"Importato Modo 5","22":"Importato Modo 6"}
    
    CH_TYPE = { "0":"0-10 volt","1":"0-2.5 volt","2":"4-20 mA","3":"0-20 mA","4":"4-24 mA","5":"0-24 mA",
               "6":"Linearizzazione","7":"Parametri da formula","8":"Non condizionato"}
    
    for key,item in appstate.iset.items():
        try:
            if 'iset.config' in key:
                row  = dict()
                row["tag"] = item['Tag']
                row["nome"] = item['Nome'] 
                row["numero"] = item['Number']                    
                row["impianto"] = item['Plant']                 
                row["motore"] = item['Engine']                  
                row["tipo"] = item['Type']
                try:
                    index = str(item["ModoOp"])
                    row["modo"] = CH_MODE[index]
                except:
                    row["modo"] = item["ModoOp"]
                try:
                    index = str(item["TipoSegnale"] )
                    row["tipo_canale"] = CH_TYPE[index]
                except:
                    row["tipo_canale"] = item["TipoSegnale"]                
                row["inizio_scala"] = round(item["InizioScala"],3)
                row["fondo_scala"] = round(item["FondoScala"] ,3)
                row["umisura"] = item["UMisura"] 
                row["stato_riposo"] = item["DNormale"] 
                row["stato_eccitato"] = item["DAllarme"]  
                row["exp_address"] = item["ExpAddress"]  

                try:
                    row["codice_sonoro_inf"] = item["AllarmeInf"]["MessaggioIn"] 
                    row["codice_sonoro_sup"] = item["AllarmeSup"]["MessaggioIn"] 
                except Exception as e:
                    logger.warning(e)

                try:
                    last = appstate.iset[key.replace('config','last')] 
                    if math.isnan(last["value"]):                        
                        row["valore"] = 0            
                    else: 
                        row["valore"] = last["value"]      
                    row["timestamp"] = datetime.fromtimestamp(last['ts']).strftime('%Y-%m-%d %H:%M:%S')                                           
                except Exception as e:
                    row["valore"] =  None
                    row["timestamp"] = datetime.fromtimestamp(0).strftime('%Y-%m-%d %H:%M:%S')    
                result.append(row)
        except:
            pass
    return result

async def get_map():

    path = f'iset/isetmap.json'
    
    try:
        read = appstate.fs.info(path) 
    except:
        isetmap =  iset_map()
        with appstate.fs.open(path, 'w') as file:
            file.write(json.dumps(isetmap))

    #crossreference from iset pages
    crosspath = "iset.last.crossreference"
    try:            
        read = appstate.fs.info(f"gis/{crosspath}.parquet")            
    except:
        try:
            rawpayload = await appstate.nc.get(crosspath)          
            with appstate.fs.open(f"gis/{crosspath}.parquet", 'wb') as file:
                file.write(rawpayload.value)
        except:
            pass 

    paths = ['gis.anagrafica.gas']
    for path in paths:
        try:            
            read = appstate.fs.info(f"gis/{path}")            
        except:
            try:
                rawpayload = await appstate.nc.get(f'iset.{path}')          
                with appstate.fs.open(f"gis/{path}.parquet", 'wb') as file:
                    file.write(rawpayload.value)
            except:
                pass 
    with duckdb.connect('sirapi.db') as conn:
        conn.register_filesystem(appstate.fs)  
        conn.execute(f"DROP TABLE IF EXISTS crossref; CREATE TABLE IF NOT EXISTS crossref AS (SELECT * FROM read_parquet('memory://gis/{crosspath}.parquet'))")
        conn.sql('select * from crossref').show()        
        #global iset map
        path = f'iset/isetmap.json'
        conn.execute(f"DROP TABLE IF EXISTS ISETMAP; CREATE TABLE IF NOT EXISTS ISETMAP AS SELECT * FROM read_json_auto('memory://{path}')")

        #gas mapped trough SIR platform
        path = 'gis/gis.anagrafica.gas.parquet'       

        query = f"""SELECT T1.*,ISETMAP.nome,ISETMAP.tag,ISETMAP.umisura FROM (SELECT  REPLACE(ID_MISURA, '.UI', '')as ID_MISURA, ID_PERIFERICA,ID_TIPOLOGIA_MISURA,COMMODITY, IDENTIFICATIVO_OGGETTO_MAPPA, LATITUDINE,LONGITUDINE
                from read_parquet('memory://{path}')) as T1 INNER JOIN ISETMAP ON ISETMAP.nome LIKE  T1.ID_MISURA  || '%' """  
        
        qcross = f"select T1.*,crossref.tag from ({query}) as T1 inner join crossref on T1.tag = crossref.tag "

        conn.query(f"DROP TABLE IF EXISTS SIRISETMAP; CREATE TABLE IF NOT EXISTS SIRISETMAP AS {qcross}")   

        conn.sql('select * from SIRISETMAP').show()

async def loop():
    
    await appstate.nc.connect() 
    #watcher = await appstate.nc.watchkv('iset.config.>')
    isetconfig = dict()
    
    schedule.every().day.at("02:15").do(etl)

    async def cb(msg):
        logger.info(f'iset hist request {msg.data.decode()} on "{msg.subject}"')
        try:
            req = json.loads(msg.data.decode().replace("'",'"'))
            tags = req['tags']
            start = req['start_date']
            end = req['end_date']
            start_date = datetime.strptime(start,"%Y-%m-%d") 
            end_date = datetime.strptime(end,"%Y-%m-%d")              
            alltags = req.get('all',False)
            isettags = list()            
            glogtags = list()
            for tag in tags:
                if tag.startswith('GA') or tag.startswith('GB') or tag.startswith('PB'):
                    glogtags.append(tag)
                else:
                    isettags.append(tag)
            result = list()

            #GLOG TYPE
            if len(glogtags) > 0 or alltags:   
                if alltags:
                    condition =''
                else:
                    condition = f"where ID_MISURA in {tuple(glogtags)}"
                with duckdb.connect('sirapi.db') as conn:
                    q = f"""SELECT  split_part(ID_MISURA, '.', 1) AS RTU,  split_part(ID_MISURA, '.', 2)  || '_UI' AS TAG
                            FROM SIRISETMAP {condition} """ #not like '%CFG%' 

                    query = f"""
                        SELECT distinct glog.rtu,glog.tag,CAST(glog.day AS VARCHAR) AS tday, glog.avg_value FROM glog
                        INNER JOIN ({q}) AS map
                        ON glog.TAG = map.TAG and glog.RTU = map.RTU
                        WHERE DAY BETWEEN DATE '{start_date}' AND DATE '{end_date}' """
                    data = conn.sql(query).fetchall()
                
                for e in data:
                    row = dict()
                    rtu = e[0]
                    tag = e[1].replace('_UI','')
                    row['tag'] = f"{rtu}.{tag}"
                    row['day'] = e[2]
                    if e[3] > 1000 or  e[3] < -1000:
                        row['avg'] = 0                    
                    else:
                        row['avg'] = e[3]
                    row['unit'] = 'bar'
                    if tag == 'PSPV':
                        row['unit'] = 'mbar'
                    result.append(row)

            #ISET TYPE
            if len(isettags) > 0 or alltags:    
                if alltags:
                    condition ='WHERE'
                else:
                    condition = f"WHERE ID_MISURA in {tuple(isettags)} AND "                            
                with duckdb.connect('sirapi.db') as conn:
                    q = f"""select tag,ID_MISURA,umisura from SIRISETMAP {condition}
                             ID_PERIFERICA NOT LIKE 'GA%' and ID_PERIFERICA NOT LIKE 'GB%' and ID_PERIFERICA NOT LIKE 'PB%'
                            """ #not like '%CFG%' 
                    query = f"""
                        SELECT distinct iset.tag,CAST(iset.day AS VARCHAR) AS tday, iset.avg_value, map.ID_MISURA,map.umisura FROM iset
                        INNER JOIN ({q}) AS map
                        ON iset.tag = map.tag 
                        WHERE DAY BETWEEN DATE '{start_date}' AND DATE '{end_date}' """

                    conn.sql(query).show()
                    data = conn.sql(query).fetchall()

                    for e in data:
                        row = dict()
                        row['tag'] = e[3]
                        row['day'] = e[1]
                        row['avg'] = e[2]
                        row['unit'] = e[4]                                    
                        result.append(row)    
                        
            await msg.respond(json.dumps(result).encode()) 
        except Exception as e:
            logger.warning(e)
            await msg.respond(json.dumps({'error':str(e)}).encode())

    await appstate.nc.sub('sir.consumer.aggregates', cb= cb)
    await appstate.nc.sub('sir.consumer.pressioni.aggregati', cb= cb)
    #await appstate.nc.sub('sir.consumer.aggregatex', cb= cb)

    logger.info('iset sir api started and listening on "sir.consumer.aggregates" & "sir.consumer.pressioni.aggregati"')

    while True:
        # hydrate state store 
        schedule.run_pending()
        await asyncio.sleep(1)

def isethist(tags,engine):
    year = datetime.now().year
    path =  f"C:\Apps\Production\Apps\iset\historian\engine={engine}\year={year}\*\IA*"              
    q = f"""
            SELECT 
                tag,
                DATE_TRUNC('day', ts) AS day,
                AVG(value) AS avg_value
            FROM 
                read_parquet(['{path}'])
            WHERE tag in {tuple(tags)}
            GROUP BY 
                tag, 
                day
            ORDER BY 
                tag, 
                day """
            
    with duckdb.connect('sirapi.db') as conn:
        #conn.execute("DROP TABLE IF EXISTS  ISET;")

        query = """CREATE TABLE IF NOT EXISTS ISET (
                tag VARCHAR,
                day DATE,
                avg_value DOUBLE,
                UNIQUE (tag, day)
                )"""
        conn.execute(query)
        #conn.execute(f" CREATE TABLE IF NOT EXISTS ISET  AS {q}")
        conn.sql(f" INSERT OR IGNORE INTO ISET {q}")
        conn.sql('select count(*) from ISET').show()

def glog():
    year = datetime.now().year
    path = f"//192.168.30.73/c_giove2/Apps/Production/Applications/Engine/cache/{year}/trends/*/*"
    q = f""" 
        SELECT 
            rtu,
            tag,
            DATE_TRUNC('day', dataora) AS day,
            AVG(valore) AS avg_value
        FROM 
            read_parquet(['{path}'])
        WHERE VALID = 1
        GROUP BY 
            rtu,
            tag, 
            day
        ORDER BY 
            rtu,
            tag, 
            day
            """

    with duckdb.connect('sirapi.db') as conn:
        #conn.execute("DROP TABLE IF EXISTS  GLOG;")

        query = """CREATE TABLE IF NOT EXISTS GLOG (
                RTU VARCHAR,
                TAG VARCHAR,
                DAY DATE,
                AVG_VALUE DOUBLE,
                UNIQUE (RTU, TAG, DAY)
                )"""
        conn.execute(query)
        #conn.execute(f" CREATE TABLE IF NOT EXISTS GLOG  AS {q}")
        conn.sql(f" INSERT OR IGNORE INTO GLOG {q}")
        conn.sql('select * from GLOG').show()

def etl():
    #glog data
    glog()
    #await asyncio.to_thread(glog)
    #iset extract load transform logic
    with duckdb.connect('sirapi.db') as conn:
        #engines
        q = """ select DISTINCT split_part(tag, '::', 1) as tag from SIRISETMAP 
                where ID_PERIFERICA NOT LIKE 'GA%' and ID_PERIFERICA NOT LIKE 'GB%' and ID_PERIFERICA NOT LIKE 'PB%' """
        conn.sql(q).show()
        engines = conn.sql(q).fetchall()

    for engine in engines:

        with duckdb.connect('sirapi.db') as conn:    
            
            q = f"""select  tag from SIRISETMAP where tag LIKE '%{engine[0]}%'
                and ID_PERIFERICA NOT LIKE 'GA%' and ID_PERIFERICA NOT LIKE 'GB%' and ID_PERIFERICA NOT LIKE 'PB%'
                    """ #not like '%CFG%' 

            data = conn.sql(q).show()

            data = conn.sql(q).fetchall()
            qtags = [item for sublist in data for item in sublist] 
                                
            query = f"""
                        SELECT 
                        tag,
                        DATE_TRUNC('day', ts) AS day,
                        AVG(value) AS avg_value
                    FROM 
                        read_parquet(table)

                    GROUP BY 
                        tag, 
                        day
                    ORDER BY 
                        tag, 
                        day
                    """  
        try:            
            #await asyncio.to_thread(isethist,qtags,engine[0])
            isethist(qtags,engine[0])
        except Exception as e:
            logger.error(e)
    logger.info('scheduled task done')        

def sirapi_runner():
    asyncio.run(loop())

if __name__ == '__main__':
    sirapi_runner()