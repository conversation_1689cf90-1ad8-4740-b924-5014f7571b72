# ISET Reader Build Script
# PowerShell script to build the application using PyInstaller

param(
    [switch]$Clean,
    [switch]$Debug,
    [switch]$NoUPX,
    [switch]$Help
)

function Show-Usage {
    Write-Host "ISET Reader Build Script" -ForegroundColor Green
    Write-Host "Usage: .\build.ps1 [options]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  -Clean    Clean build directories before building"
    Write-Host "  -Debug    Build with debug information"
    Write-Host "  -NoUPX    Disable UPX compression"
    Write-Host "  -Help     Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Magenta
    Write-Host "  .\build.ps1                    # Standard build"
    Write-Host "  .\build.ps1 -Clean             # Clean build"
    Write-Host "  .\build.ps1 -Debug -NoUPX      # Debug build without compression"
}

if ($Help) {
    Show-Usage
    exit 0
}

Write-Host "ISET Reader Build Script" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
Write-Host ""

# Check if PyInstaller is installed
try {
    $pyinstallerVersion = & pyinstaller --version 2>$null
    Write-Host "PyInstaller version: $pyinstallerVersion" -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: PyInstaller not found. Installing..." -ForegroundColor Red
    pip install pyinstaller
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install PyInstaller. Exiting." -ForegroundColor Red
        exit 1
    }
}

# Check if UPX is available (optional)
$upxAvailable = $false
try {
    $upxVersion = & upx --version 2>$null
    if ($upxVersion) {
        $upxAvailable = $true
        Write-Host "UPX available for compression" -ForegroundColor Cyan
    }
} catch {
    Write-Host "UPX not found - compression will be disabled" -ForegroundColor Yellow
}

# Clean build directories if requested
if ($Clean) {
    Write-Host "Cleaning build directories..." -ForegroundColor Yellow
    if (Test-Path "build") {
        Remove-Item -Recurse -Force "build"
        Write-Host "Removed build directory" -ForegroundColor Green
    }
    if (Test-Path "dist") {
        Remove-Item -Recurse -Force "dist"
        Write-Host "Removed dist directory" -ForegroundColor Green
    }
    if (Test-Path "__pycache__") {
        Remove-Item -Recurse -Force "__pycache__"
        Write-Host "Removed __pycache__ directory" -ForegroundColor Green
    }
    # Remove .pyc files
    Get-ChildItem -Recurse -Filter "*.pyc" | Remove-Item -Force
    Write-Host "Removed .pyc files" -ForegroundColor Green
}

# Prepare build arguments
$buildArgs = @()

if ($Debug) {
    Write-Host "Building with debug information..." -ForegroundColor Yellow
    $buildArgs += "--debug=all"
    $buildArgs += "--console"
} else {
    Write-Host "Building optimized release version..." -ForegroundColor Green
}

if ($NoUPX -or -not $upxAvailable) {
    $buildArgs += "--noupx"
    Write-Host "UPX compression disabled" -ForegroundColor Yellow
}

# Add common arguments
$buildArgs += "--clean"
$buildArgs += "--noconfirm"
$buildArgs += "--onefile"
$buildArgs += "--specpath=."

# Create dist directory if it doesn't exist
if (-not (Test-Path "dist")) {
    New-Item -ItemType Directory -Path "dist" | Out-Null
}

# Build the application
Write-Host ""
Write-Host "Starting build process..." -ForegroundColor Green
Write-Host "Build arguments: $($buildArgs -join ' ')" -ForegroundColor Cyan

$buildCommand = "pyinstaller"
$allArgs = $buildArgs + @("isetreader.spec")

Write-Host "Executing: $buildCommand $($allArgs -join ' ')" -ForegroundColor Cyan
Write-Host ""

try {
    & $buildCommand @allArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Build completed successfully!" -ForegroundColor Green
        
        # Check if executable was created
        $exePath = "dist\isetreader.exe"
        if (Test-Path $exePath) {
            $fileInfo = Get-Item $exePath
            $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
            Write-Host "Executable created: $exePath" -ForegroundColor Green
            Write-Host "File size: $fileSizeMB MB" -ForegroundColor Cyan
            
            # Copy config.toml to dist directory
            if (Test-Path "config.toml") {
                Copy-Item "config.toml" "dist\" -Force
                Write-Host "Copied config.toml to dist directory" -ForegroundColor Green
            } else {
                Write-Host "WARNING: config.toml not found. Create one in the dist directory." -ForegroundColor Yellow
            }
            
            # Create log directory in dist
            $logDir = "dist\log"
            if (-not (Test-Path $logDir)) {
                New-Item -ItemType Directory -Path $logDir | Out-Null
                Write-Host "Created log directory in dist" -ForegroundColor Green
            }
            
            Write-Host ""
            Write-Host "Build Summary:" -ForegroundColor Green
            Write-Host "  Executable: $exePath" -ForegroundColor White
            Write-Host "  Size: $fileSizeMB MB" -ForegroundColor White
            Write-Host "  Config: dist\config.toml" -ForegroundColor White
            Write-Host "  Logs: dist\log\" -ForegroundColor White
            Write-Host ""
            Write-Host "To run the application:" -ForegroundColor Yellow
            Write-Host "  cd dist" -ForegroundColor White
            Write-Host "  .\isetreader.exe" -ForegroundColor White
            
        } else {
            Write-Host "ERROR: Executable not found at $exePath" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "Build failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "Build failed with error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Build process completed!" -ForegroundColor Green
