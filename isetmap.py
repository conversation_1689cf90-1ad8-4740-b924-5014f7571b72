import asyncio
from datetime import datetime
from messaging import component
import json
import duckdb
import fsspec
import nats.errors
from datetime import datetime
import schedule
import math
from config import app_config

toml_config = app_config('config.toml')

def iset_map(iset):
    result = list()
    CH_MODE = {"0":"Fuori Scansione", "1":"In Scansione","2": "Scansione e limiti","3": "Trasmiss. evento",     
                "4":"Allarme", "5":"Allarme inferiore","6":"Allarme superiore","7":"Non Valido","8":"Canale esportato",
                "17":"Importato Modo 1", "18":"Importato Modo 2","19":"Importato Modo 3",
                "20":"Importato Modo 4","21":"Importato Modo 5","22":"Importato Modo 6"}
    
    CH_TYPE = { "0":"0-10 volt","1":"0-2.5 volt","2":"4-20 mA","3":"0-20 mA","4":"4-24 mA","5":"0-24 mA",
               "6":"Linearizzazione","7":"Parametri da formula","8":"Non condizionato"}
    
    for key,item in iset.items():
        try:
            if 'iset.config' in key:
                row  = dict()
                row["tag"] = item['Tag']
                row["nome"] = item['Nome'] 
                row["numero"] = item['Number']                    
                row["impianto"] = item['Plant']                 
                row["motore"] = item['Engine']                  
                row["tipo"] = item['Type']
                try:
                    index = str(item["ModoOp"])
                    row["modo"] = CH_MODE[index]
                except:
                    row["modo"] = item["ModoOp"]
                try:
                    index = str(item["TipoSegnale"] )
                    row["tipo_canale"] = CH_TYPE[index]
                except:
                    row["tipo_canale"] = item["TipoSegnale"]                
                row["inizio_scala"] = round(item["InizioScala"],3)
                row["fondo_scala"] = round(item["FondoScala"] ,3)
                row["umisura"] = item["UMisura"] 
                row["stato_riposo"] = item["DNormale"] 
                row["stato_eccitato"] = item["DAllarme"]  
                row["exp_address"] = item["ExpAddress"]  

                try:
                    row["codice_sonoro_inf"] = item["AllarmeInf"]["MessaggioIn"] 
                    row["codice_sonoro_sup"] = item["AllarmeSup"]["MessaggioIn"] 
                except Exception as e:
                    print(e)

                try:
                    last = iset[key.replace('config','last')] 
                    if math.isnan(last["value"]):                        
                        row["valore"] = 0            
                    else: 
                        row["valore"] = last["value"]      
                    row["timestamp"] = datetime.fromtimestamp(last['ts']).strftime('%Y-%m-%d %H:%M:%S')                                           
                except Exception as e:
                    row["valore"] =  None
                    row["timestamp"] = datetime.fromtimestamp(0).strftime('%Y-%m-%d %H:%M:%S')    
                result.append(row)
        except:
            pass
    return result

async def get_map():
    nc = component(toml_config['nats'])
    iset = dict()
    fs = fsspec.filesystem('memory')  

    await nc.connect(bucket = 'iset') 
    watcher = await nc.watchkv('iset.config.>')
    #prefetch
    lastresult = await watcher.updates(timeout=2)
    while lastresult is not None:
        try:
            lastresult = await watcher.updates(timeout=2)
            if lastresult is not None:
                try:
                    iset[lastresult.key] = json.loads(lastresult.value.decode())
                  
                except Exception as e:
                    ('prefetch watcher error',e)
        except nats.errors.TimeoutError as e:
            pass   
        except Exception as e:
            print('prefetch error',e) 


    path = f'iset/isetmap.json'
    
    try:
        read = fs.info(path) 
    except:
        isetmap =  iset_map(iset)
        with fs.open(path, 'w') as file:
            file.write(json.dumps(isetmap))

    #crossreference from iset pages
    crosspath = "iset.last.crossreference"
    try:            
        read = fs.info(f"gis/{crosspath}.parquet")            
    except:
        try:
            rawpayload = await nc.get(crosspath)          
            with fs.open(f"gis/{crosspath}.parquet", 'wb') as file:
                file.write(rawpayload.value)
        except:
            pass 

    paths = ['gis.anagrafica.gas']
    for path in paths:
        try:            
            read = fs.info(f"gis/{path}")            
        except:
            try:
                rawpayload = await nc.get(f'iset.{path}')          
                with fs.open(f"gis/{path}.parquet", 'wb') as file:
                    file.write(rawpayload.value)
            except:
                pass 
    with duckdb.connect('sirapi.db') as conn:
        conn.register_filesystem(fs)  
        conn.execute(f"DROP TABLE IF EXISTS crossref; CREATE TABLE IF NOT EXISTS crossref AS (SELECT * FROM read_parquet('memory://gis/{crosspath}.parquet'))")
        conn.sql('select * from crossref').show()        
        #global iset map
        path = f'iset/isetmap.json'
        conn.execute(f"DROP TABLE IF EXISTS ISETMAP; CREATE TABLE IF NOT EXISTS ISETMAP AS SELECT * FROM read_json_auto('memory://{path}')")

        #gas mapped trough SIR platform
        path = 'gis/gis.anagrafica.gas.parquet'       

        query = f"""SELECT T1.*,ISETMAP.nome,ISETMAP.tag,ISETMAP.umisura FROM (SELECT  REPLACE(ID_MISURA, '.UI', '')as ID_MISURA, ID_PERIFERICA,ID_TIPOLOGIA_MISURA,COMMODITY, IDENTIFICATIVO_OGGETTO_MAPPA, LATITUDINE,LONGITUDINE
                from read_parquet('memory://{path}')) as T1 INNER JOIN ISETMAP ON ISETMAP.nome LIKE  T1.ID_MISURA  || '%' """  
        
        qcross = f"select T1.*,crossref.tag from ({query}) as T1 inner join crossref on T1.tag = crossref.tag "

        conn.query(f"DROP TABLE IF EXISTS SIRISETMAP; CREATE TABLE IF NOT EXISTS SIRISETMAP AS {qcross}")   

        conn.sql('select * from SIRISETMAP').show()

def get_iset_map():
    asyncio.run(get_map())