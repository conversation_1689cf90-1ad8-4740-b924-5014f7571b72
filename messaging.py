import nats
import asyncio
import ssl

class component:
    def __init__(self, config):
        self.config = config
        self._nc = None
        self._js = None
        self._kv = None
        self._os = None
        self._bucket = None
        self._stream = None
        self._obj = None

        self._done = asyncio.Future()

    async def connect(self, *args, **kwargs):    
            #self._nc = await nats.connect(*args, **kwargs)
            ssl_ctx = None
            if self.config.get('ca',None) and self.config.get('certificate',None) and self.config.get('key',None) :                        
                ssl_ctx = ssl.create_default_context(purpose=ssl.Purpose.SERVER_AUTH)
                ssl_ctx.load_verify_locations(self.config['ca'])
                ssl_ctx.load_cert_chain(certfile=self.config['certificate'],
                                        keyfile=self.config['key'])
            else:
                print("No ssl context, running unencrypted connection")
            
            user = self.config.get('username',None)
            password = self.config.get('password',None)
            self._nc = await nats.connect(servers = self.config['servers'],
                                    user = user,
                                    password = password ,
                                    connect_timeout = 3,
                                    #pending_size = 0, #20 * 1024 * 1024,    
                                    tls=ssl_ctx)

            self._js = self._nc.jetstream()
            
            if "bucket" in kwargs:
                self._bucket = kwargs["bucket"]
                self._kv = await self._js.create_key_value(bucket = kwargs['bucket'], max_bytes = 100000000)
            
            if "stream" in kwargs:
                self._stream = kwargs["stream"]
                await self._js.add_stream(name = self._stream, subjects = [f"ScadaApi.js.{self._stream}"], num_replicas = 3, max_bytes = 20000000)

            if "obj" in kwargs:
                self._obj = kwargs["obj"]
                self._os = await self._js.create_object_store(bucket = kwargs['obj'])
            
    async def run_forever(self):
        await self._done

    async def close(self):
        await self._nc.close()
        if self._done:
            self._done.cancel()
        asyncio.get_running_loop().stop()
    
    ######### PUBSUB METHODS #########
    async def sub(self, *args, **kwargs):
        return await self._nc.subscribe(*args, **kwargs)

    async def pub(self, *args, **kwargs):
        await self._nc.publish(*args, **kwargs)
    
    ######### REQ/REPLY ##########
    async def req(self, *args, **kwargs):
        return await self._nc.request(*args, **kwargs)
    
    ######### KV METHODS #########
    async def put(self,*args, **kwargs):
        revision = await self._kv.put(*args,**kwargs)
        return revision
    
    async def get(self,*args, **kwargs):        
        value = await self._kv.get(*args,**kwargs)
        return value
    
    async def update(self,*args, **kwargs):        
        value = await self._kv.update(*args,**kwargs)
        return value
    
    async def delete(self,*args, **kwargs):        
        value = await self._kv.purge(*args,**kwargs)
        return value
        
    async def keys(self,*args, **kwargs):        
        value = await self._kv.keys(*args,**kwargs)
        return value
    
    async def watchkv(self,*args, **kwargs):        
        value = await self._kv.watch(*args,**kwargs)
        return value

    async def key_value(self,*args, **kwargs):
            value = await self._js.create_key_value(*args,**kwargs)
            #value = await self._js.key_value(*args,**kwargs)
            return value      
       
    async def watch(self, *args, **kwargs):
        arglist = list(args)
        arglist [0] = f"$KV.{self._bucket}.{arglist[0]}"
        args = tuple(arglist)
        return await self.sub(*args, **kwargs)

    ######### ObjectStore METHODS #########
    async def objput(self,*args, **kwargs):
        revision = await self._os.put(*args,**kwargs)
        return revision
    
    async def objget(self,*args, **kwargs):        
        value = await self._os.get(*args,**kwargs)
        return value
    
    async def objkeys(self,*args, **kwargs):        
        value = await self._os.list(*args,**kwargs)
        return value
    
    async def objwatchnotworking(self,*args, **kwargs):        
        value = await self._os.watch(*args,**kwargs)
        return value

    async def object_store(self,*args, **kwargs):
            value = await self._js.create_object_store(*args,**kwargs)
            return value      
       
    async def objwatch(self, *args, **kwargs):
        arglist = list(args)
        arglist [0] = f"$OBJ.{self._obj}.{arglist[0]}"
        args = tuple(arglist)
        return await self.sub(*args, **kwargs)    
    
    ######### JS METHODS #########
    async def psub(self, *args, **kwargs):
        return await self._js.pull_subscribe(*args, **kwargs)

    async def qpub(self, *args):
        await self._js.publish(*args, stream = self._stream)

    async def jssub(self, *args, **kwargs):
        await self._js.subscribe(*args, **kwargs )