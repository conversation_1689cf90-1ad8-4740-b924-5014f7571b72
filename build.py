#!/usr/bin/env python3
"""
ISET Reader Build Script
Python script to build the application using PyInstaller
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path


class Colors:
    """ANSI color codes for terminal output"""
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    CYAN = '\033[96m'
    MAGENTA = '\033[95m'
    WHITE = '\033[97m'
    RESET = '\033[0m'
    BOLD = '\033[1m'


def print_colored(message, color=Colors.WHITE):
    """Print colored message to console"""
    print(f"{color}{message}{Colors.RESET}")


def print_header(title):
    """Print a formatted header"""
    print_colored(f"\n{title}", Colors.GREEN + Colors.BOLD)
    print_colored("=" * len(title), Colors.GREEN)


def check_pyinstaller():
    """Check if PyInstaller is installed and install if needed"""
    try:
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True, check=True)
        version = result.stdout.strip()
        print_colored(f"PyInstaller version: {version}", Colors.CYAN)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_colored("PyInstaller not found. Installing...", Colors.YELLOW)
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], 
                         check=True)
            print_colored("PyInstaller installed successfully", Colors.GREEN)
            return True
        except subprocess.CalledProcessError:
            print_colored("Failed to install PyInstaller", Colors.RED)
            return False


def check_upx():
    """Check if UPX is available"""
    try:
        subprocess.run(['upx', '--version'], 
                      capture_output=True, check=True)
        print_colored("UPX available for compression", Colors.CYAN)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_colored("UPX not found - compression will be disabled", Colors.YELLOW)
        return False


def clean_build_dirs():
    """Clean build directories"""
    print_colored("Cleaning build directories...", Colors.YELLOW)
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print_colored(f"Removed {dir_name} directory", Colors.GREEN)
    
    # Remove .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))
    
    print_colored("Removed .pyc files", Colors.GREEN)


def get_file_size_mb(filepath):
    """Get file size in MB"""
    if os.path.exists(filepath):
        size_bytes = os.path.getsize(filepath)
        return round(size_bytes / (1024 * 1024), 2)
    return 0


def build_application(debug=False, no_upx=False, upx_available=False):
    """Build the application using PyInstaller"""
    print_colored("Starting build process...", Colors.GREEN)
    
    # Prepare build arguments
    build_args = ['pyinstaller']
    
    # Common arguments
    build_args.extend([
        '--clean',
        '--noconfirm',
    ])
    
    # Debug options
    if debug:
        print_colored("Building with debug information...", Colors.YELLOW)
        build_args.extend(['--debug=all', '--console'])
    else:
        print_colored("Building optimized release version...", Colors.GREEN)
    
    # UPX compression
    if no_upx or not upx_available:
        build_args.append('--noupx')
        print_colored("UPX compression disabled", Colors.YELLOW)
    
    # Add spec file
    build_args.append('isetreader.spec')
    
    print_colored(f"Build command: {' '.join(build_args)}", Colors.CYAN)
    print()
    
    # Execute build
    try:
        result = subprocess.run(build_args, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print_colored(f"Build failed with exit code: {e.returncode}", Colors.RED)
        return False


def post_build_setup():
    """Setup files after successful build"""
    exe_path = Path('dist') / 'isetreader.exe'
    
    if not exe_path.exists():
        print_colored(f"ERROR: Executable not found at {exe_path}", Colors.RED)
        return False
    
    # Get file size
    file_size = get_file_size_mb(exe_path)
    print_colored(f"Executable created: {exe_path}", Colors.GREEN)
    print_colored(f"File size: {file_size} MB", Colors.CYAN)
    
    # Copy config.toml to dist directory
    config_src = Path('config.toml')
    config_dst = Path('dist') / 'config.toml'
    
    if config_src.exists():
        shutil.copy2(config_src, config_dst)
        print_colored("Copied config.toml to dist directory", Colors.GREEN)
    else:
        print_colored("WARNING: config.toml not found. Create one in the dist directory.", Colors.YELLOW)
    
    # Create log directory in dist
    log_dir = Path('dist') / 'log'
    log_dir.mkdir(exist_ok=True)
    print_colored("Created log directory in dist", Colors.GREEN)
    
    return True, exe_path, file_size


def print_build_summary(exe_path, file_size):
    """Print build summary"""
    print_header("Build Summary")
    print_colored(f"  Executable: {exe_path}", Colors.WHITE)
    print_colored(f"  Size: {file_size} MB", Colors.WHITE)
    print_colored(f"  Config: dist{os.sep}config.toml", Colors.WHITE)
    print_colored(f"  Logs: dist{os.sep}log{os.sep}", Colors.WHITE)
    print()
    print_colored("To run the application:", Colors.YELLOW)
    print_colored("  cd dist", Colors.WHITE)
    if os.name == 'nt':  # Windows
        print_colored("  .\\isetreader.exe", Colors.WHITE)
    else:  # Unix-like
        print_colored("  ./isetreader", Colors.WHITE)


def main():
    """Main build function"""
    parser = argparse.ArgumentParser(description='ISET Reader Build Script')
    parser.add_argument('--clean', action='store_true', 
                       help='Clean build directories before building')
    parser.add_argument('--debug', action='store_true',
                       help='Build with debug information')
    parser.add_argument('--no-upx', action='store_true',
                       help='Disable UPX compression')
    parser.add_argument('--help-build', action='store_true',
                       help='Show detailed build help')
    
    args = parser.parse_args()
    
    if args.help_build:
        print_header("ISET Reader Build Script Help")
        print_colored("This script builds the ISET Reader application into a single executable.", Colors.WHITE)
        print()
        print_colored("Options:", Colors.CYAN)
        print_colored("  --clean     Clean build directories before building", Colors.WHITE)
        print_colored("  --debug     Build with debug information", Colors.WHITE)
        print_colored("  --no-upx    Disable UPX compression", Colors.WHITE)
        print()
        print_colored("Examples:", Colors.MAGENTA)
        print_colored("  python build.py                    # Standard build", Colors.WHITE)
        print_colored("  python build.py --clean            # Clean build", Colors.WHITE)
        print_colored("  python build.py --debug --no-upx   # Debug build without compression", Colors.WHITE)
        return
    
    print_header("ISET Reader Build Script")
    
    # Check PyInstaller
    if not check_pyinstaller():
        sys.exit(1)
    
    # Check UPX
    upx_available = check_upx()
    
    # Clean if requested
    if args.clean:
        clean_build_dirs()
    
    # Create dist directory if it doesn't exist
    Path('dist').mkdir(exist_ok=True)
    
    # Build the application
    success = build_application(
        debug=args.debug,
        no_upx=args.no_upx,
        upx_available=upx_available
    )
    
    if not success:
        print_colored("Build failed!", Colors.RED)
        sys.exit(1)
    
    # Post-build setup
    success, exe_path, file_size = post_build_setup()
    if not success:
        sys.exit(1)
    
    # Print summary
    print_build_summary(exe_path, file_size)
    
    print_colored("\nBuild process completed successfully!", Colors.GREEN + Colors.BOLD)


if __name__ == '__main__':
    main()
