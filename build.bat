@echo off
REM ISET Reader Build Script - Batch version
REM Simple batch script to build the application using PyInstaller

setlocal enabledelayedexpansion

echo ISET Reader Build Script
echo ========================
echo.

REM Check if PyInstaller is installed
pyinstaller --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: PyInstaller not found. Installing...
    pip install pyinstaller
    if errorlevel 1 (
        echo Failed to install PyInstaller. Exiting.
        pause
        exit /b 1
    )
) else (
    echo PyInstaller found
)

REM Check for clean parameter
if "%1"=="clean" (
    echo Cleaning build directories...
    if exist "build" rmdir /s /q "build"
    if exist "dist" rmdir /s /q "dist"
    if exist "__pycache__" rmdir /s /q "__pycache__"
    del /s /q "*.pyc" >nul 2>&1
    echo Cleanup completed
    echo.
)

REM Create dist directory if it doesn't exist
if not exist "dist" mkdir "dist"

echo Starting build process...
echo.

REM Build the application
pyinstaller --clean --noconfirm --onefile --specpath=. isetreader.spec

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

REM Check if executable was created
if exist "dist\isetreader.exe" (
    echo.
    echo Build completed successfully!
    
    REM Copy config.toml to dist directory
    if exist "config.toml" (
        copy "config.toml" "dist\" >nul
        echo Copied config.toml to dist directory
    ) else (
        echo WARNING: config.toml not found. Create one in the dist directory.
    )
    
    REM Create log directory in dist
    if not exist "dist\log" mkdir "dist\log"
    echo Created log directory in dist
    
    echo.
    echo Build Summary:
    echo   Executable: dist\isetreader.exe
    echo   Config: dist\config.toml
    echo   Logs: dist\log\
    echo.
    echo To run the application:
    echo   cd dist
    echo   isetreader.exe
    
) else (
    echo ERROR: Executable not found at dist\isetreader.exe
    pause
    exit /b 1
)

echo.
echo Build process completed!
pause
