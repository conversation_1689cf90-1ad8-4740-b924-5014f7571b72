
import re
import json

class ISETSynPanel:
    def __init__(self, widgets = dict(), resource = None) -> None:
        self.size = {'width' :0, 'height':0} 
        self.location = {'x' :0, 'y':0}
        self.wallpaperName = "" 
        self.imgurl = ""
        self.layers = 0
        self.svg = None
        self.id = ""
        self.title = ""
        self.tags = {}
        self.getui(widgets, resource)
    
    def getsize(self,size):
            data = re.search('\{(.*?)\}', size).group(1)
            h = data.split(',')[0].replace('Width=','').strip()
            w = data.split(',')[1].replace('Height=','').strip()
            self.size  = {'width' :h, 'height':w} 

    def getlocation(self,loc):
            data = re.search('\{(.*?)\}', loc).group(1)
            x = data.split(',')[0].replace('X=','').strip()
            y = data.split(',')[1].replace('Y=','').strip()
            self.location  = {'x' :x, 'y':y} 
    
    def getui(self,widgets,resource):
           page_index = ""
           svgchildren=""
           for index,e in widgets.items():
                page_index = index
                self.title = e['title'] 
                self.getsize(e['isetsynpanel']['size'])
                self.getlocation(e['isetsynpanel']['location'])
                self.imgurl = e['isetsynpanel']['imgurl']  + e['isetsynpanel']['wallpapername']
                for _,widget in e['isetsynpanel']['widgets'].items():
                      
                        if widget['type'] == "isetanalogiclabel":
                                data = ISETAnalogicLabel(widget['props'])
                                svgchildren += data.svg
                                self.tags[data.rtitem] = "float"

                        if widget['type'] == "isetdigitallabel":
                                data = ISETDigitalLabel(widget['props'])
                                svgchildren += data.svg   
                                self.tags[data.rtitem] = "string"       

                        if widget['type'] == "isettransparentarea-null":
                                data = ISETTransparentArea(widget['props'])
                                svgchildren += data.svg   
                                self.tags[data.rtitem] = "string" 

                        if widget['type'] == "isetdynamicblock":
                                data = ISETDynamicBlock(widget['props'])
                                svgchildren += data.svg   
                                self.tags[data.rtitem] = 0     

                        if widget['type'] == "isetblinkingmultistatefill":   
                            data = ISETBlinkingMultiStateFill(widget['props'])
                            svgchildren += data.svg   
                            for tag in data.tags:
                                self.tags[tag] = 0    

                        if widget['type'] == "isetmultiimage":  
                            try: 
                                data = ISETMultiImage(widget['props'], resource)
                                svgchildren += data.svg   
                                for tag in data.tags:
                                    self.tags[tag] = 0   
                            except Exception as e:
                                print(e,widget['props'])   
                       
                        if widget['type'] == "isetdocommandbutton":  
                            try: 
                                data = ISETDOCommandButton(widget['props'])
                                svgchildren += data.svg   
                                for tag in data.tags:
                                    self.tags[tag] = 0   
                            except Exception as e:
                                print(e,widget['props'])    
                        
                        if widget['type'] == "isetguicommandbutton":  
                            try: 
                                data = ISETGuiCommandButton(widget['props'])
                                svgchildren += data.svg    
                            except Exception as e:
                                print(e,widget['props']) 
                                                
                                                                                                                                                                                                                                      
           self.id = f"ISETSynPanel"
           
           self.svg =  f"""<svg  class="size-full" viewBox="0 0 {self.size['width']} {self.size['height']}"  id="{self.id}" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                hx-ext="sse" hx-swap="none" sse-connect="/webscada/realtime/synoptics/{page_index}" sse-swap="message" >
                                <foreignObject width="100%" height="100%">
                                    <body xmlns="http://www.w3.org/1999/xhtml">
                                        <canvas id="canvas" width="{self.size['width']}" height="{self.size['height']}"></canvas>
                                    </body>
                                </foreignObject>{svgchildren}
                                <script>
                                <![CDATA[""
                                    var img = new Image();
                                    img.src = "{self.imgurl}"
                                    var canvas = document.getElementById("canvas");
                                    if(isMobileDevice())
                                        var ctxCanvas = canvas.getContext("2d", {{ willReadFrequently: true }});
                                    else
                                        var worker = new Worker('static/js/graphics.js');
                                    var fillers = document.getElementsByTagName('circle');
                                    var coordinates = []

                                    
                                    
                                    for (let i in fillers){{                                        
                                        try{{
                                            if (fillers[i].getAttribute("type") == "filler"){{
                                                //get value of the point to be floodfilled
                                                let states = JSON.parse(fillers[i].attributes[4].value);  
                                                for(let tag in states){{
                                                    //states[tag][0].rgba = colorNameToRGBA(states[tag][0].color);   
                                                    for(let i = 0; i< states[tag].length; i++ )  
                                                        states[tag][i].rgba = colorNameToRGBA(states[tag][i].color);                                             
                                                }}
                                                coordinates.push([fillers[i].cx.baseVal.value, fillers[i].cy.baseVal.value, states]);
                                            }}
                                            }}catch{{;}}
                                    }}
                                    img.onload = async function() {{

                                        canvas.width = this.width;
                                        canvas.height =  this.height;         
                                        img.style.display = "none";
                                        const svgElement = document.getElementById('ISETSynPanel');
                                        const viewBox = '0 0 '+this.width +' '+this.height;
                                        svgElement.setAttribute('viewBox', viewBox); 
                                        if (isMobileDevice()){{
                                            ctxCanvas.drawImage(img, 0, 0);
                                        }}else{{
                                            const canvasWorker = canvas.transferControlToOffscreen();
                                            const imgblob = await fetch("{self.imgurl}")
                                            .then(r => r.blob())
                                            .then(blob => {{
                                                worker.postMessage({{'imgblob':blob,'canvas':canvasWorker}},[canvasWorker]);  
                                            }});                                          
                                        }}                                                      
                                    }};                                    
                                    /*                                    
                                    img.onload = function() {{
                                                canvas.width = this.width;
                                                canvas.height =  this.height;         
                                                ctx.drawImage(img, 0, 0);
                                                img.style.display = "none";
                                                const svgElement = document.getElementById('ISETSynPanel');
                                                const viewBox = '0 0 '+this.width +' '+this.height;
                                                svgElement.setAttribute('viewBox', viewBox);  
                                                //flicker();      
                                                //setInterval(flicker, 1000);                                                 
                                            }};
                                    */

                                    var previous = {{}};
                                    var blink  = {{}}
                                    var toggle = false;
                                    
                                    function fillDeprecateed(result) {{  
                                            for( let e in coordinates){{ 
                                                let data = coordinates[e];
                                                let states = JSON.parse(data[2]);                                                                                          
                                                
                                                for (let tag in states){{
                                                    //console.log(data[0],data[1],tag)
                                                    if(result[tag]){{  
                                                        for (let i in states[tag]){{
                                                            let boolState = 0;
                                                            if(states[tag][i].state == 'true')
                                                                boolState = 1;
                                                            if(result[tag].value == boolState){{
                                                                //console.log('xxx',result[tag].value,states[tag][i]);
                                                                //console.log('xxx',result[tag].value,states[tag]);

                                                                try{{  
                                                                        //if(previous[tag] && (previous[tag] !=result[tag].value)){{
                                                                            floodFill(ctx, data[0], data[1],  colorNameToRGBA(states[tag][i].color));
                                                                            if(states[tag].blink == 'true'){{
                                                                                blink[tag] = {{'x':data[0],'y':data[1],'on':[255,0,0,1], 'off':[0,255,0,1] }}
                                                                            }}
                                                                            previous[tag] = result[tag].value;
                                                                        
                                                                        //console.log('done---------------',tag,'x',data[0],'y',data[1])
                                                                        //}}
                                                                    }}catch(e){{console.log(e)}}
                                                            }}
                                                        }}
                                                    }}
                                                    
                                                  }}   
                                             }}                               
                                    }} 
                                    function flicker() {{
                                        
                                            for( let e in coordinates){{ 
                                                let data = coordinates[e]    
                                                const states = JSON.parse(data[2]);                                                                                          
                                                console.log(states);
                                                try{{                                                    
                                                    if (!toggle){{
                                                        floodFill(ctx, data[0], data[1], colorNameToRGBA('lime'));
                                                    }}else{{
                                                        floodFill(ctx, data[0], data[1], colorNameToRGBA('red'));
                                                    }}
                                                }}catch(e){{}}
                                            
                                            }}                                        
                                        toggle = !toggle;  
                                    }}         
                                    var svgElement = document.getElementById("{self.id}");
                                    //var elements;
                                    var units = [1,1];
                                    var toggleUnit = false;
                                    function convertUnits(u1,u2){{
                                        toggleUnit = !toggleUnit;
                                        if (toggleUnit == true){{
                                            units = [u1,u2];
                                        }}else{{
                                            units = [1,1];
                                            }}                                      
                                        /*
                                        let elements = document.getElementsByTagName("text");
                                        console.log(elements)
                                        for(let i = 0; i < elements.length; i++ ){{    
                                            try{{
                                       
                                                if(elements[i].getAttribute('type') == 'analog'){{
                                                    console.log(elements[i],element.innerHTML)
                                                     result = (parseFloat(elements[i].innerHTML) * u1);
                                                     elements[i].innerHTML = results.toFixed(2);
                                                 }}
                                            }}catch{{}}
                                        }}*/
                                    }}

                                    function roundNumber(num) {{
                                        // Round the number to two decimal places
                                        let rounded = num.toFixed(2);
                                        let floatNum = parseFloat(rounded);
                                        if (floatNum % 1 === 0)
                                            return floatNum.toFixed(0);
                                        else 
                                            return rounded;  
                                    }}                                    
                                    var DoButtons = document.querySelectorAll(`[type="DoButton"]`);
                                    htmx.on('htmx:sseMessage', (msg)=> {{
                                            let result = JSON.parse(msg.detail.data);  
                                            
                                            for (let e in result)
                                                result[e]['rgba'] = colorNameToRGBA(result[e]['color']) 

                                            //fill(result);       
                                            if(isMobileDevice())
                                                fill(ctxCanvas, result, coordinates);
                                            else
                                                worker.postMessage({{ 'result': result, 'coordinates': coordinates}});

                                            for (var tag in result) {{       
                                                 //var element = document.getElementById(tag); 
                                                 let elements = document.querySelectorAll(`[id='${{tag}}']`);
                                                  
                                                 for(let i = 0; i < elements.length; i++ ){{
                                                    if (elements[i].id.includes(tag)){{
                                                        element = elements[i]
                                                        if (element){{
                                                                if(result[tag].color && element.getAttribute('type') == 'analog'){{
                                                                    element.style.fill = result[tag].color;
                                                                    element.innerHTML = result[tag].value;
                                                                    if(result[tag].unit.toLowerCase() === 'm')
                                                                        element.innerHTML = roundNumber(result[tag].value * 1); //units[0]
                                                                    if(result[tag].unit.toLowerCase() === 'l/s')
                                                                        element.innerHTML = roundNumber(result[tag].value * units[1]);   
                                                                    element.innerHTML = result[tag].value; //forced                                                                 
                                                                }}
                                                                if(element.getAttribute('type') == 'digital'){{                                                           
                                                                    element.style.fill = result[tag].color;                                                                                                                      
                                                                    let value = result[tag].value;    
                                                                    if (element.getAttribute('content') == 'mode'){{
                                                                                value = result[tag].stato;
                                                                            }}                                                                     
                                                                    if(value == 0){{
                                                                                element.innerHTML = element.getAttribute('normaltxt');
                                                                                }}
                                                                    else if(value == 1){{
                                                                                element.innerHTML = element.getAttribute('alarmtxt');
                                                                                }} 
                                                                    else if (value == "F.Sc."){{
                                                                        element.innerHTML ="FS"
                                                                        element.style.fill = 'grey'
                                                                    }}
                                                                    //console.log(element.getAttribute('content'))                                                                   
                                                                                                                            
                                                            }} 
                                                             if (element.id == tag & element.getAttribute('content') == 'units'){{
                                                                        element.innerHTML = result[tag].unit;
                                                                }}   
                                                            }}                                                        
                                                    }}
                                                  }}      
                                                                                                                                                           
                                                const levelElement = document.getElementById('level-'+tag);
                                                const tsElement = document.getElementById('ts-'+tag);

                                                //timestamp
                                                if (tsElement){{
                                                            tsElement.innerHTML = result[tag].ts;
                                                    }}    

                                                if(levelElement){{                                                
                                                    let leveltag = tag.replace('level-','');
                                                    let maxLevel= Math.abs(levelElement.getAttribute('maxlevel'))
                                                    let minLevel= Math.abs(levelElement.getAttribute('minlevel'))
                                                    let max = levelElement.getAttribute('max')
                                                    let origin = levelElement.getAttribute('origin')
                                                    let level = ((result[leveltag].value + minLevel)/(maxLevel+minLevel)) *  max ;
                                                    levelElement.setAttribute('height',level);
                                                    levelElement.setAttribute('y',parseFloat(origin) + (max-level));
                                                }}    

                                                //DOButton element                                                  
                                                if(DoButtons.length > 0) {{  
                                                    for (let i in DoButtons){{                                                                                         
                                                            try{{                                                                                                                                                                                                                                                                            
                                                                if (DoButtons[i].getAttribute("type") == "DoButton"){{
                                                                    DoButtons[i].setAttribute('fill','lime');
                                                                    /* 
                                                                    //get value of the point to be floodfilled
                                                                    let boolState = 0;
                                                                    let states = JSON.parse(DoButtons[i].getAttribute("states"));  
                                                                    for(let tag in states){{
                                                                        //states[tag][0].rgba = colorNameToRGBA(states[tag][0].color);  
                                                                        
                                                                        for(let j = 0; j< states[tag].length; j++ ) {{                                                                             
                                                                            if(states[tag][j].state == 'true')
                                                                                boolState = 1;
                                                                            if (result[tag].value == boolState){{
                                                                                console.log(tag,states[tag],result[tag],states[tag][j].color)
                                                                                DoButtons[i].setAttribute('fill',states[tag][j].color); 
                                                                            }}  
                                                                            //DoButtons[i].setAttribute('fill',states[tag][j].color);
                                                                        }}                                          
                                                                    }} 
                                                                    */                                                               
                                                                }}
                                                            }}catch{{}}
                                                        }}
                                                }}
                                                //image resources
                                                var images = svgElement.getElementsByTagName("image");
                                                // Loop through the collection and log each image's href attribute
                                                for (var i = 0; i < images.length; i++) {{                                                
                                                    var states = JSON.parse(images[i].getAttribute("states"))
                                                    
                                                    for (let tag in states){{
                                                        for (var j = 0; j < states[tag].length; j++) {{
                                                            
                                                            if (result.hasOwnProperty(tag)){{
                                                            
                                                                if(typeof result[tag].value === 'number' && !isNaN(result[tag].value)){{
                                                                    var checkValue = result[tag].value === 0 ? 'false' : 'true'}}
                                                                   
                                                                    if (checkValue == states[tag][j].state){{
                                                                        src = '/webscada/resources/' + images[i].id.replace('::','') + states[tag][j].img + '.png';
                                                                        imageHref = images[i].getAttribute("href")
                                                                        if (imageHref != src){{                                                                            
                                                                            images[i].setAttribute('href', src);
                                                                            //console.log('image',imageHref,'src',src)
                                                                        }}                                                                  
                                                                    }}

                                                            }}
                                                        }} 
                                                    }}          
                                                
                                                }}   //for end                                                                                       
                                            }}
                                    }})
                                    document.getElementById('{self.id}').addEventListener("contextmenu",(e) => {{
                                            e.preventDefault();
                                            let left = e.pageX;
                                            let top = e.pageY;
                                            let ctxMenu = document.getElementById("context-menu");
                                            if (e.srcElement.id.includes('::')){{                                                
                                                ctxMenu.style.top = `${{e.pageY}}px`;
                                                ctxMenu.style.left =  `${{e.pageX}}px`;
                                                ctxMenu.classList.remove('hidden');
                                            }}
                                        }});

                                    document.getElementById('{self.id}').addEventListener("click",(e) => {{
                                        let ctxMenu = document.getElementById("context-menu");                                        
                                        if (e.srcElement.id.includes('::') & (e.srcElement.tagName == 'image' | e.srcElement.tagName == 'circle')){{                                                                                           
                                            vals = JSON.parse(e.srcElement.getAttribute('hx-vals'))
                                            htmx.ajax('GET', '/webscada/multistate/', {{target:'#context-menu', swap:'innerHTML', values: vals}} ).then(() => {{
                                                    ctxMenu.style.top = `${{e.pageY}}px`;
                                                    ctxMenu.style.left =  `${{e.pageX}}px`;
                                                    ctxMenu.classList.remove('hidden');
                                                    }});
                                        }}else{{
                                            ctxMenu.classList.add('hidden');
                                        }}                                   
                                    }});

                                    htmx.on('htmx:sseError', (e)=> {{
                                        console.log(e)
                                        //window.location.href = "/webscada"
                                    }})  
                                    function isMobileDevice() {{
                                        return /Mobi|Android/i.test(navigator.userAgent) || window.matchMedia("(max-width: 768px)").matches;
                                    }}                                  
                                     ]]> 
                                </script>
                            </svg>"""           
                     
           svgname = page_index.split('.')[1] 
           '''
           with open (f"pages\{svgname} {self.title}.svg",'w') as f:
                f.write(self.svg)
           '''

class ISETAnalogicLabel :
        def __init__(self,props = dict()) -> None:

            self.size = {"width" :"0", "height":"0"} 
            self.location = {"x" :"0", "y":"0"} 
            self.usecompatibletextrendering = True
            self.period = 200 
            self.font ="[Font: Name=Microsoft Sans Serif, Size=11, Bold=True]" 
            self.commandlayers ="0" 
            self.normalcolor ="Color [Lime]" 
            self.rtitem ="" 
            self.enableOutOfScan = True 
            self.outOfScanText = "F.Sc." 
            self.outOfScanColor = "Color [Silver]" 
            self.failureColor = "Color [DodgerBlue]" 
            self.highAlarmColor = "Color [Tomato]" 
            self.lowAlarmColor = "Color [Yellow]" 
            self.failuretext = "anom"
            self.enableFailure = True
            self.enableAlarmColors = True
            self.extraText = "" 
            self.content = "" #attribute to view the timestamp
            self.textalign = ""
            self.max = 0
            self.valueTransformation = None
            self.svg = None
            self.id = None
            self.getui(props)

        def getsize(self,size):
                data = re.search('\{(.*?)\}', size).group(1)
                h = data.split(',')[0].replace('Width=','').strip()
                w = data.split(',')[1].replace('Height=','').strip()
                self.size  = {'width' :int(h), 'height':int(w)} 
    
        def getlocation(self,loc):
                data = re.search('\{(.*?)\}', loc).group(1)
                x = data.split(',')[0].replace('X=','').strip()
                y = data.split(',')[1].replace('Y=','').strip()
                self.location  = {'x' :int(x), 'y':int(y)} 
        
        def getcolor(self,color):            
            return re.search('\[(.*?)\]', color).group(1).lower()

        def getfont(self,font):
            return re.search('\[(.*?)\]', font).group(1)

        def getui(self,props):
            self.rtitem = props['rtitem']   
            self.getsize(props['size'])
            self.getlocation(props['location'])
            try:
                self.normalcolor = self.getcolor(props['normalcolor']).lower()
            except:
                self.normalcolor = 'lime'
        
            '''             
            self.normalcolor = self.getcolor(self.normalcolor)
            self.outOfScanColor = self.getcolor(self.outOfScanColor) 
            self.failureColor = self.getcolor(self.failureColor) 
            self.highAlarmColor = self.getcolor(self.highAlarmColor)
            self.lowAlarmColor = self.getcolor(self.lowAlarmColor)            
            self.id = f"{self.id}_{self.location['x']}{self.location['y']}"
            '''
            xlocation = self.location['x']+(self.size['width']/2)
            ylocation = self.location['y']+(self.size['height']/2)
            self.textalign = props.get('textalign','').lower()
            self.content = props.get('content','value').lower()
            align = ''
            if self.textalign == 'middlecenter':
                align = f'dominant-baseline="middle" text-anchor="middle"'

            self.svg =  f'''<text type="analog" content="{self.content}" {align} hx-get=/webscada/config/{self.rtitem}/?chartsIndex=-1 hx-target="#context-menu"  hx-swap="innerHtml" hx-trigger="contextmenu" id="{self.rtitem}" x="{xlocation}" y="{ylocation}" fill="lime" font-weight="bold" font-size ="12" font-family="Microsoft Sans Serif" dominant-baseline="middle" text-anchor="middle"></text>
                         '''                    

class ISETDynamicBlock :
        def __init__(self,props = dict()) -> None:
            
            self.size = {"width" :"0", "height":"0"} 
            self.location = {"x" :"0", "y":"0"} 
            self.usecompatibletextrendering = True
            self.max = 0
            self.min = 0
            self.rtitem ="" 
            self.failureColor ="Color [Empty]"
            self.emptywarningvalue ="50"
            self.emptywarningcolor ="Color [Red]"
            self.enableemptywarning ="True"
            self.highprealarmcolor ="Color [Empty]"
            self.lowprealarmcolor ="Color [Empty]"
            self.highalarmcolor ="Color [LightBlue]"
            self.lowalarmcolor ="Color [Yellow]"
            self.forecolor ="Color [Aqua]"
            self.enablealarmcolors ="True"
            self.max = ""
            self.min = ""
            self.svg = None
            self.id = None
            self.getui(props)

        def getsize(self,size):
                data = re.search('\{(.*?)\}', size).group(1)
                h = data.split(',')[0].replace('Width=','').strip()
                w = data.split(',')[1].replace('Height=','').strip()
                self.size  = {'width' :int(h), 'height':int(w)} 
    
        def getlocation(self,loc):
                data = re.search('\{(.*?)\}', loc).group(1)
                x = data.split(',')[0].replace('X=','').strip()
                y = data.split(',')[1].replace('Y=','').strip()
                self.location  = {'x' :int(x), 'y':int(y)} 
        
        def getcolor(self,color):            
            return re.search('\[(.*?)\]', color).group(1)
        
        def getui(self,props):
            self.rtitem = props['rtitem']   
            self.getsize(props['size'])
            self.getlocation(props['location'])
            self.min = props.get('min','0').replace(',','.')  
            self.max = props.get('max', self.size['height']).replace(',','.')  
            try:
                self.forecolor = self.getcolor(props['forecolor']).lower()
            except:
                self.forecolor = self.getcolor(props['highalarmcolor']).lower()

            self.svg =  f'''<rect type="level"  hx-get=/webscada/config/{self.rtitem}/?chartsIndex=-1 hx-target="#context-menu"  hx-swap="innerHtml" hx-trigger="contextmenu" minlevel="{self.min}" maxlevel="{self.max}" origin="{self.location['y']}" max="{self.size['height']}" id="level-{self.rtitem}" x="{self.location['x']}" y="{self.location['y']}" width="{self.size['width']}" height="{self.size['height']}" fill="{self.forecolor}" />
                         '''                    

class ISETDigitalLabel:
        def __init__(self,props = dict()) -> None:
            
            self.size = {"width" :"0", "height":"0"} 
            self.location = {"x" :"0", "y":"0"} 
            self.usecompatibletextrendering = True
            self.period = "200"
            self.min = 0
            self.rtitem ="" 
            self.font ="[Font: Name=Microsoft Sans Serif, Size=11, Bold=True]" 
            self.commandlayers ="0" 
            self.normalcolor ="Color [Green]" 
            self.alarmbackcolor ="Color [Transparent]"
            self.normalbackcolor ="Color [Transparent]"
            self.textalign = ""
            self.alarmtext = ""
            self.normaltext = ""
            self.content = "" #attribute to view the could be value/timestamp/mode
            self.svg = None
            self.id = None
            self.getui(props)

        def getsize(self,size):
                data = re.search('\{(.*?)\}', size).group(1)
                h = data.split(',')[0].replace('Width=','').strip()
                w = data.split(',')[1].replace('Height=','').strip()
                self.size  = {'width' :int(h), 'height':int(w)} 
    
        def getlocation(self,loc):
                data = re.search('\{(.*?)\}', loc).group(1)
                x = data.split(',')[0].replace('X=','').strip()
                y = data.split(',')[1].replace('Y=','').strip()
                self.location  = {'x' :int(x), 'y':int(y)} 
        
        def getcolor(self,color):            
            return re.search('\[(.*?)\]', color).group(1)
        
        def getui(self,props):
            self.rtitem = props['rtitem']   
            self.getsize(props['size'])
            self.getlocation(props['location'])
            self.normalcolor = self.getcolor(props['normalcolor']).lower()
            xlocation = self.location['x']+(self.size['width']/2)
            ylocation = self.location['y']+(self.size['height']/2) 
            align =f'dominant-baseline="middle" text-anchor="middle"'
            self.alarmtext = props['alarmtext'] 
            self.normaltext = props['normaltext']      
            self.content = props.get('content','value').lower()
            self.textalign = props.get('textalign','').lower()

            if self.textalign == 'middlecenter':
                xlocation = self.location['x']+(self.size['width']/2)
                ylocation = self.location['y']+(self.size['height']/2) 
                align = f'dominant-baseline="middle" text-anchor="middle"'

            #self.location['x'] self.location['y']+self.size['height']
            prefix = f"" #f"{xlocation}{ylocation}-"
            if self.content == 'longtimestamp':
                  prefix ="ts-"
            self.svg =  f'''<text  type="digital"  hx-get=/webscada/config/{self.rtitem}/?chartsIndex=-1 hx-target="#context-menu"  hx-swap="innerHtml" hx-trigger="contextmenu" id="{prefix}{self.rtitem}" x="{xlocation }" y="{ylocation}" {align} fill="{self.normalcolor}"  content="{self.content}" alarmtxt="{self.alarmtext}" normaltxt="{self.normaltext}" font-weight="bold" font-size ="12" font-family="Microsoft Sans Serif" ></text>
                         '''                  

class ISETTransparentArea:

        """
                <ISETTransparentArea>
                <Size value="{Width=10, Height=32}" />
                <Location value="{X=1256,Y=776}" />
                <DesignPlacemark value="False" />
                <AllowCmdModeSetting value="True" />
                <CommandLayers value="0" />
                <TargetType value="Tag" />
                <RTItem value="TNP::203030031" />
                <Layers value="0" />
                </ISETTransparentArea>
        """ 
                
        def __init__(self,props = dict()) -> None:
            
            self.size = {"width" :"0", "height":"0"} 
            self.location = {"x" :"0", "y":"0"} 
            self.rtitem ="" 
            self.font ="[Font: Name=Microsoft Sans Serif, Size=11, Bold=True]" 
            self.commandlayers ="0" 
            self.normalcolor ="Green" 
            self.alarmbackcolor ="Tomato"
            self.normalbackcolor ="Transparent"
            self.textalign = ""
            self.alarmtext = ""
            self.normaltext = ""
            self.content = "" #attribute to view the could be value/timestamp/mode
            self.svg = None
            self.id = None
            self.getui(props)

        def getsize(self,size):
                data = re.search('\{(.*?)\}', size).group(1)
                h = data.split(',')[0].replace('Width=','').strip()
                w = data.split(',')[1].replace('Height=','').strip()
                self.size  = {'width' :int(h), 'height':int(w)} 
    
        def getlocation(self,loc):
                data = re.search('\{(.*?)\}', loc).group(1)
                x = data.split(',')[0].replace('X=','').strip()
                y = data.split(',')[1].replace('Y=','').strip()
                self.location  = {'x' :int(x), 'y':int(y)} 
        
        def getcolor(self,color):            
            return re.search('\[(.*?)\]', color).group(1)
        
        def getui(self,props):
            self.rtitem = props['rtitem']   
            self.getsize(props['size'])
            self.getlocation(props['location'])
            #self.normalcolor = self.getcolor(props['normalcolor']).lower()
            xlocation = self.location['x']+(self.size['width']/2)
            ylocation = self.location['y']+(self.size['height']) 
            align = ''#f'dominant-baseline="middle" text-anchor="middle"'
            self.alarmtext = 'A' 
            self.normaltext = 'M'      
            self.content = props.get('content','value').lower()
            self.textalign = props.get('textalign','').lower()

            if self.textalign == 'middlecenter':
                xlocation = self.location['x']+(self.size['width']/2)
                ylocation = self.location['y']+(self.size['height']/2) 
                align = f'dominant-baseline="middle" text-anchor="middle"'

            #self.location['x'] self.location['y']+self.size['height']
            prefix = ""
            if self.content == 'longtimestamp':
                  prefix ="ts-"
            self.svg =  f'''<text  type="digital"  hx-get=/webscada/config/{self.rtitem}/?chartsIndex=-1 hx-target="#context-menu"  hx-swap="innerHtml" hx-trigger="contextmenu" id="{prefix}{self.rtitem}" x="{xlocation }" y="{ylocation}" {align} fill="{self.normalcolor}"  content="{self.content}" alarmtxt="{self.alarmtext}" normaltxt="{self.normaltext}" font-weight="bold" font-size ="12" font-family="Microsoft Sans Serif" ></text>
                         '''                  
                            
class ISETBlinkingMultiStateFill:
        def __init__(self,props = dict()) -> None:
            self.location = {"x" :"0", "y":"0"}    
            self.items = dict()  
            self.blinkingmode = "" 
            self.svg = ""
            self.tags = list()
            self.getui(props)

        def getlocation(self,loc):
                data = re.search('\{(.*?)\}', loc).group(1)
                x = data.split(',')[0].replace('X=','').strip()
                y = data.split(',')[1].replace('Y=','').strip()
                self.location  = {'x' :int(x), 'y':int(y)}                  
        
        def getcolor(self,color):            
            return re.search('\[(.*?)\]', color).group(1)   
            
        def getstates(self,items):
                 matches = re.findall(r'\{(.*?)\}', items)   
                 #{id=IS150::72010035, color=Color [Lime], state=False, blink=False}
                 for data in matches:
                       datalist = data.split(',')
                       tag = datalist[0].replace('id=','').strip().upper()
                       self.tags.append(tag)
                       id = f"fill-{tag}"
                       color = self.getcolor(datalist[1].replace('color=','').strip()).lower()
                       state = datalist[2].replace('state=','').strip().lower()
                       blink = datalist[3].replace('blink=','').strip().lower()
                       #self.items += f'tag="{tag}" color="{color}" state={state} blink={blink} '
                       if tag not in self.items:
                              self.items[tag] = []
                       self.items[tag].append({'color':color,'state':state,'blink':blink})

        def getui(self,props):   
            self.getlocation(props['location'])   
            self.getstates(props['items'])       
            offset = 5       
            tags = ','.join(list(self.items.keys()))

            self.svg =  f'''<circle id="{self.location['x']}::{self.location['y']}" cx="{self.location['x']+offset}" cy="{self.location['y']+offset}"  r="8" states='{json.dumps(self.items)}' type="filler" fill-opacity="0" hx-get=/webscada/multiconfig/ hx-target="#context-menu"  hx-swap="innerHtml" hx-trigger="contextmenu" hx-vals='{json.dumps({"tags":tags})}'></circle>'''            

class ISETDOCommandButton:
        def __init__(self,props = dict()) -> None:
            self.location = {"x" :"0", "y":"0"}  
            self.size = {"width" :"0", "height":"0"}   
            self.items = dict()  
            self.blinkingmode = "" 
            self.svg = ""
            self.tags = list()
            self.rtitem  = ""
            self.getui(props)

        def getsize(self,size):
                data = re.search('\{(.*?)\}', size).group(1)
                h = data.split(',')[0].replace('Width=','').strip()
                w = data.split(',')[1].replace('Height=','').strip()
                self.size  = {'width' :int(h), 'height':int(w)} 

        def getlocation(self,loc):
                data = re.search('\{(.*?)\}', loc).group(1)
                x = data.split(',')[0].replace('X=','').strip()
                y = data.split(',')[1].replace('Y=','').strip()
                self.location  = {'x' :int(x), 'y':int(y)}                  
        
        def getcolor(self,color):            
            return re.search('\[(.*?)\]', color).group(1)   
            
        def getstates(self,tag):

                 #{id=IS150::72010035, color=Color [Lime], state=False, blink=False}
                 #{'IS150::84010033': [{'color': 'lime', 'state': 'false', 'blink': 'false'}], 'IS150::84010001': [{'color': 'red', 'state': 'true', 'blink': 'true'}]
                 conditions = {'false':'lime','true':'red'}
                 for key,color in conditions.items():
                       self.tags.append(tag)
                       color = color
                       state = key
                       blink = 'false'
                       #self.items += f'tag="{tag}" color="{color}" state={state} blink={blink} '
                       if tag not in self.items:
                              self.items[tag] = []
                       self.items[tag].append({'color':color,'state':state,'blink':blink})

        def getui(self,props):  
            self.rtitem = props['rtitem']   
            self.getsize(props['size'])
            self.getlocation(props['location'])   
            self.getstates(self.rtitem)       
            offset = 0       
            tags = ','.join([self.rtitem])

            self.svg =  f'''<circle type="DoButton" id="{self.location['x']}::{self.location['y']}" cx="{self.location['x']+(self.size['width']/2)}"  cy="{self.location['y']+(self.size['height']/2) }"  r="8" states='{json.dumps(self.items)}' fill = "grey" hx-get=/webscada/multiconfig/ hx-target="#context-menu"  hx-swap="innerHtml" hx-trigger="contextmenu" hx-vals='{json.dumps({"tags":tags})}'></circle>'''            
 
class ISETGuiCommandButton:
        def __init__(self,props = dict()) -> None:
            self.location = {"x" :"0", "y":"0"}  
            self.size = {"width" :"0", "height":"0"}    
            self.svg = ""
            self.getui(props)

        def getsize(self,size):
                data = re.search('\{(.*?)\}', size).group(1)
                h = data.split(',')[0].replace('Width=','').strip()
                w = data.split(',')[1].replace('Height=','').strip()
                self.size  = {'width' :int(h), 'height':int(w)} 

        def getlocation(self,loc):
                data = re.search('\{(.*?)\}', loc).group(1)
                x = data.split(',')[0].replace('X=','').strip()
                y = data.split(',')[1].replace('Y=','').strip()
                self.location  = {'x' :int(x), 'y':int(y)}                  

        def getui(self,props):    
            self.getsize(props['size'])
            self.getlocation(props['location'])
            self.svg =  f'''<rect type="GuiCmdBtn" x="{self.location['x']}"  y="{self.location['y'] }"  width="{self.size['width']}" height="{self.size['height']}" onclick="convertUnits(10.691,3.596)" fill-opacity="0" />'''            
 
class ISETMultiImage:
        def __init__(self,props = dict(), resource = None) -> None:
            self.size = {"width" :"0", "height":"0"} 
            self.location = {"x" :"0", "y":"0"}  
            self.id = props.get("id","")
            self.items = dict()  
            self.svg = ""
            self.tags = list()
            self.img_list = dict()
            self.resource = resource
            self.getui(props)

        def getsize(self,size):
                data = re.search('\{(.*?)\}', size).group(1)
                h = data.split(',')[0].replace('Width=','').strip()
                w = data.split(',')[1].replace('Height=','').strip()
                self.size  = {'width' :int(h), 'height':int(w)} 

        def getlocation(self,loc):
                data = re.search('\{(.*?)\}', loc).group(1)
                x = data.split(',')[0].replace('X=','').strip()
                y = data.split(',')[1].replace('Y=','').strip()
                self.location  = {'x' :int(x), 'y':int(y)}                  
        
        def getcolor(self,color):            
            return re.search('\[(.*?)\]', color).group(1)   
            
        def getstates(self,items):
                 
                 matches = re.findall(r'\{(.*?)\}', items)   
                 #{id=IS150::72010035, color=Color [Lime], state=False, blink=False}
                 for key, img in self.resource.items():
                    if self.id in key:
                        name =key.split('.img')[-1]
                        index = int(name)                     
                        self.img_list[index] = (img,f'.img{name}')

                 for i,data in enumerate(matches):
                    try:
                        datalist = data.split(',')
                        tag = datalist[0].replace('id=','').strip().upper()
                        self.tags.append(tag)
                        #color = self.getcolor(datalist[1].replace('color=','').strip()).lower()
                        state = ""
                        if len(datalist) > 1:
                            state = datalist[1].replace('state=','').strip().lower()
                        #self.items += f'tag="{tag}" color="{color}" state={state} blink={blink} '
                        if tag not in self.items:
                            self.items[tag] = []  
                        
                        #wicked...!
                        if len(self.items[tag]) > 0:
                             state = 'false'
                             if self.items[tag][0].get('state','') == 'false':
                                  state = 'true' 
                        else:
                             if len(state) == 0:
                                  state = 'true'                              
                        self.items[tag].append({'state':state,'img':self.img_list[i][1]})
                    except Exception as e:
                          print(e)
                 
        def getui(self,props):   
            self.getlocation(props['location'])   
            self.getsize(props['size'])
            self.getstates(props['items'])     

            offset = 0      
            tags = ','.join(list(self.items.keys()))            

            for key,value in self.img_list.items():
                #print(key, self.id, self.location['x'],self.location['y'])
                try:

                    #xlink:href="data:image/png;base64,{value[0]}" 
                    self.svg +=  f'''<image href="/webscada/resources/{self.id}.img0000.png" 
                                    id="{self.id}::" width="{self.size['width']}" height="{self.size['height']}" 
                                    x="{self.location['x']+offset}" y="{self.location['y']+offset}"  
                                    states='{json.dumps(self.items)}' hx-get=/webscada/multiconfig/ hx-target="#context-menu"  
                                    hx-swap="innerHtml" hx-trigger="contextmenu" hx-vals='{json.dumps({"tags":tags})}'/>'''            
                except Exception as e:
                     print('Widgetmultiimage Error',e)      