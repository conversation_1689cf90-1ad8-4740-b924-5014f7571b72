import re
import glob
import xml.etree.ElementTree as ET
import duckdb
import fsspec
import fsspec.implementations.memory
import json
import sys 
import os
import time
import asyncio
from messaging import component

import logging
from logging.handlers import RotatingFileHandler
import os
from config import app_config

toml_config = app_config('config.toml')

"""
this app reads the gui files from iset and extract the cross reference between the tags and the pages, publishes it to nats "iset" kv bucket at the topic "iset.last.crossreference" in parquet format.
other services can subscribe to this bucket and watch for changes or get the cross reference data.
"""
# create log folder
if not os.path.isdir("log"):
    os.makedirs('log')

#logger
logger = logging.getLogger('crossreference')
logger.setLevel(logging.INFO)

# Create a rotating file handler
log_file = f"./log/crossreference.log"
handler = RotatingFileHandler(log_file, maxBytes=1024*1024*50, backupCount=2)
formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
handler.setFormatter(formatter)

# Console handler
console_handler = logging.StreamHandler()
console_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(handler)
logger.addHandler(console_handler)

def reverse_bits(num, bits=10):
  binary_string = bin(num)[2:].zfill(bits)
  reversed_binary = binary_string[::-1]
  reversed_num = int(reversed_binary, 2)
  return reversed_num

def get_tree(user = None, ui_path = None):
    pages = {}
    html = ""
    i = 0

    #fetch root element
    root = ET.parse(ui_path).getroot()

    #getpage tree
    tree = root.findall('.//page')

    for e in tree:
        page = e.attrib
        if 'name' in page.keys(): 
            name = page['name'] 
        try:    
            if 'index' in page.keys():     
                    ply = page['ply'] 
                    index = int(page['index'])
                    key =  f"page.{index}.config" 
                    if index < 1000:
                        key =  f"page.0{index}.config"                     
                    if index < 100:
                        key =  f"page.00{index}.config" 
                    if index < 10:
                        key =  f"page.000{index}.config" 

                    pages[key] = {'page': f"{index} - {name}", 'ply':ply}
            i +=1
        except Exception as e:
            print(e)
    return pages

def extract_tags(filename):
    strings = []
    with open(filename, 'r') as f:
        filetext = f.read()
        pattern = r'id=\w+?::\w+'
        matches = re.findall(pattern, filetext)
        result = [match.replace('id=', '') for match in matches]
        return result

async def cross(config):    
    path = config['iset']['isetgui']
    pages_file = f'{path}pages.config'
    old_ts = dict()   
    old_ts[pages_file] = os.path.getmtime(pages_file)
    nc = component(toml_config['nats'])
    await nc.connect(bucket = 'iset')
   
    async def read_cross_reference(path, old_ts, nc):
        cross_reference = list()
        logger.info('reading cross reference ')
        pages = get_tree(ui_path = f'{path}pages.config')
        for index, value in pages.items():
            page = value['page']
            page_grant = value['ply']
            
            number = int(page_grant,16)
            reversed_number = reverse_bits(number,bits=10) 
            gui_file = f'{path}{index}'
            try:
                tags = extract_tags (gui_file)                  
                for tag in tags:
                    row = {'page':page,'index':index,'tag':tag,'grant':reversed_number}    
                    cross_reference.append(row)
                old_ts[gui_file] =  os.path.getmtime(gui_file)
            except Exception as e:
                logger.warning(e)
        
        with duckdb.connect() as conn:       
            # Create a memory filesystem and write the parquet data to it
            fs = fsspec.filesystem('memory')
            with fs.open(f'data.json', 'w') as file:
                file.write(json.dumps(cross_reference))
            # Register the memory filesystem and create the table
            conn.register_filesystem(fs)
            #conn.query(f"COPY (SELECT * FROM read_json_auto('memory://data.json')) TO 'cross_reference.parquet' (FORMAT 'parquet');")
            #conn.query(f"SELECT * FROM read_parquet('cross_reference.parquet')").show() 
            conn.sql(f"COPY (SELECT * FROM read_json_auto('memory://data.json'))  TO 'memory://cross_reference.parquet'  (FORMAT 'parquet', CODEC 'zstd', USE_TMP_FILE 'false') ")
            conn.sql(f"select * from read_parquet('memory://cross_reference.parquet')").show()
            with fs.open('cross_reference.parquet', 'rb') as file:
                payload = file.read()
                await nc.put("iset.last.crossreference", payload)            
            #conn.query(f"SELECT count(*) FROM read_parquet('cross_reference.parquet')").show()

        logger.info('reading cross reference finished successfully')
            
    await read_cross_reference(path, old_ts, nc)
    logger.info(f'{len(old_ts)} pages found')
    return   

def crossref_runner(config):
    asyncio.run(cross(config))

if __name__ == '__main__':
    crossref_runner()
