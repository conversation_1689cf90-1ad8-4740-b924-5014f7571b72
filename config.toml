#config file for iset reader

title = "Iset reader config"

[iset]
isetini = "\\\\*************\\C_Server1_Scada\\Windows\\iset.ini"
isetgui = "\\\\*************\\C_Server1_Scada\\Iset\\Idro.NetWork\\Data\\DR\\Gui\\"
isetusers = "\\\\*************\\C_Server1_Scada\\Iset\\Idro.NetWork\\Data\\Server\\Scada\\Users\\DR\\users.data"
historian_parquet = "C:/Apps/Production/Apps/iset/historian"

[nats]
#server endpoints, put one if you have only one
servers = ["nats://localhost:4222" ]
#ssl certificates if using encrypted nats communication, comment if not using ssl
#ca = "\\\\*************\\Apps\\Production\\Services\\Nats\\CA certificates 4 clients\\ca.pem"
#certificate = "\\\\*************\\Apps\\Production\\Services\\Nats\\CA certificates 4 clients\\client-cert.pem"
#key = "\\\\*************\\Apps\\Production\\Services\\Nats\\CA certificates 4 clients\\client-key.pem"
#nats credentials, comment if not using auth
#username = "amine" 
#password = "tellmeaboutnode2natsplease"