# ISET Reader

A comprehensive Python application for reading, processing, and distributing ISET (Industrial Supervisory Control and Data Acquisition) data through a modern messaging architecture.

## Overview

ISET Reader is a multi-process application that interfaces with ISET proprietary data formats, processes real-time and historical data, and distributes it through NATS messaging system. The application handles alarms, configuration synchronization, data polling, and provides API services for data consumers.

## Features

### 🔄 **Multi-Process Architecture**
- **Alarm Processing**: Real-time alarm monitoring and archival
- **Configuration Sync**: Automatic ISET configuration synchronization
- **Data Polling**: Continuous monitoring of ISET data files
- **API Services**: RESTful API for historical data queries
- **Cross-Reference**: Data mapping and correlation services
- **SIR API**: Integration with SIR platform services
- **OPC Services**: OPC communication handling

### 📊 **Data Processing**
- **Binary Data Parsing**: Reverse-engineered ISET proprietary formats
- **Real-time Processing**: Live data streaming and processing
- **Historical Data**: Archive processing and querying
- **Parquet Storage**: Efficient columnar data storage
- **DuckDB Integration**: High-performance analytical queries

### 🚀 **Messaging & Distribution**
- **NATS Integration**: Modern messaging with JetStream support
- **Key-Value Storage**: Distributed configuration and state management
- **Event Streaming**: Real-time data distribution
- **API Endpoints**: HTTP/REST interfaces for data access

### 🔧 **Configuration Management**
- **External TOML Configuration**: Easy configuration management
- **Network Path Support**: Access to remote ISET installations
- **Flexible Deployment**: Support for various network topologies

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ISET Server   │    │  ISET Reader    │    │   Consumers     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Config Files│ │◄───┤ │Config Sync  │ │    │ │ Web SCADA   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Data Files  │ │◄───┤ │Data Polling │ │    │ │ Analytics   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Alarm Files │ │◄───┤ │Alarm Monitor│ │    │ │ Dashboards  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    │ ┌─────────────┐ │    └─────────────────┘
                       │ │ NATS Broker │ │              ▲
                       │ └─────────────┘ │              │
                       └─────────────────┘              │
                                │                       │
                                └───────────────────────┘
```

## Installation

### Prerequisites
- **Python 3.11+**
- **Network access** to ISET server
- **NATS server** (can be local or remote)

### Dependencies Installation
```bash
# Install from requirements.txt
pip install -r requirements.txt

# Or install key dependencies manually
pip install nats-py duckdb numpy pyarrow asyncio schedule
```

## Configuration

### config.toml
The application uses a TOML configuration file for all settings:

```toml
[iset]
isetini = "iset.ini"
isetgui = "Iset\\Idro.NetWork\\Data\\DR\\Gui\\"
isetusers = "Iset\\Idro.NetWork\\Data\\Server\\Scada\\Users\\DR\\users.data"
historian_parquet = "C:/Apps/Production/Apps/iset/historian"

[nats]
servers = ["nats://localhost:4222"]
# Optional SSL configuration
# ca = "path/to/ca.pem"
# certificate = "path/to/client-cert.pem"
# key = "path/to/client-key.pem"
# username = "user"
# password = "pass"
```

## Running the Application

### Development Mode
```bash
# Run directly with Python
python isetreader.py
```

### Production Deployment
```bash
# Build executable (see Build section below)
python build.py --clean

# Run the executable
cd dist
./isetreader.exe
```

## Build Process

The application can be compiled into a single executable using PyInstaller.

### Quick Build
```bash
# Standard build
python build.py

# Clean build (recommended for first build)
python build.py --clean

# Debug build with verbose output
python build.py --debug --no-upx
```

### Build Options
- `--clean`: Remove previous build artifacts
- `--debug`: Build with debug information
- `--no-upx`: Disable UPX compression
- `--help-build`: Show detailed build help

### Build Output
```
dist/
├── isetreader.exe     # Main executable
├── config.toml        # Configuration file (external)
└── log/               # Log directory
```

### Build Features
- **Single executable**: All dependencies bundled
- **External configuration**: config.toml remains external for easy updates
- **Multiprocessing support**: Proper freeze support for all processes
- **Optimized size**: UPX compression and module exclusions
- **Cross-platform**: Builds on Windows, Linux, and macOS

## Docker Deployment

For containerized deployment, Docker Compose files are provided:

```bash
# Start with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f isetreader

# Stop services
docker-compose down
```

## Application Components

### Core Modules

#### **isetreader.py** - Main Orchestrator
- Coordinates all processes
- Handles process lifecycle management
- Implements scheduled tasks
- Provides process monitoring and restart logic

#### **iset.py** - Data Processing Engine
- Binary data parsing and reverse engineering
- Historical data extraction
- Parquet file generation
- DuckDB integration for analytics

#### **isetalarms.py** - Alarm Management
- Real-time alarm monitoring
- Alarm archival and storage
- Event correlation and processing
- NATS publishing for alarm distribution

#### **isetapi.py** - API Services
- HTTP/REST API endpoints
- Historical data queries
- Real-time data access
- Integration with external systems

#### **messaging.py** - NATS Integration
- NATS connection management
- JetStream configuration
- Key-value store operations
- Publish/subscribe patterns

### Supporting Modules

- **isetconfigsync.py**: Configuration synchronization
- **isetwatchpolling.py**: Last realtime files system monitoring
- **crossreference.py**: Data mapping and correlation with the scada pages
- **sirapi.py**: SIR platform integration
- **opcservice.py**: OPC communication
- **isetmap.py**: Geographic data mapping

## Logging

The application provides comprehensive logging:

```
log/
├── iset.log           # Main application logs
├── isetalarms.log     # Alarm processing logs
├── isetapi.log        # API service logs
├── isetconfig.log     # Configuration sync logs
├── isetwatch.log      # File monitoring logs
├── opcservice.log     # OPC communication logs
└── sirapi.log         # SIR integration logs
```

## Data Flow

1. **Configuration Sync**: Reads ISET server configuration
2. **File Monitoring**: Watches for data file changes
3. **Data Processing**: Parses binary data formats
4. **Storage**: Saves processed data to Parquet/DuckDB
5. **Distribution**: Publishes data via NATS messaging
6. **API Access**: Provides HTTP endpoints for consumers

## Performance

- **Multi-process architecture** for parallel processing
- **Efficient binary parsing** with optimized algorithms (numpy)
- **Columnar storage** with Parquet for fast queries
- **In-memory processing** with DuckDB for analytics
- **Asynchronous I/O** for network operations

## Monitoring

- **Process health checks** with automatic restart
- **Comprehensive logging** with rotation
- **NATS monitoring** via web interface
- **Performance metrics** and statistics

## Troubleshooting

### Common Issues

1. **Network Path Access**: Ensure proper network credentials and paths
2. **NATS Connection**: Verify NATS server availability and configuration
3. **File Permissions**: Check read/write access to data directories
4. **Process Crashes**: Review logs for specific error messages

### Debug Mode
```bash
# Run with debug logging
python build.py --debug
cd dist
./isetreader.exe
```

## Contributing

1. Follow Python PEP 8 style guidelines
2. Add comprehensive logging for new features
3. Update configuration documentation
4. Test with both development and production data

## License

this sofware is the property of Novareti

## Support

For technical support and questions:
- Review application logs in the `log/` directory
- Check NATS server connectivity
- Verify network path accessibility
- Consult the build documentation for compilation issues
