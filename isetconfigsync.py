import struct
import json
from configparser import Config<PERSON><PERSON><PERSON>
from messaging import component
import asyncio
import re
from datetime import datetime
import csv
import os
import logging
from logging.handlers import RotatingFileHandler
import os
from config import app_config
"""
this app extract the propritary config files  from iset config for each device and plant
the extracted files gets converted to json and stored in a Nats bucket "iset" under the keys 
iset.config.{engine}.{plant}.info for the general info and iset.config.{engine}.{plant}.{io} for the io config
"""
# create log folder
if not os.path.isdir("log"):
    os.makedirs('log')

#logger
logger = logging.getLogger('isetconfig')
logger.setLevel(logging.INFO)

# Create a rotating file handler
log_file = f"./log/isetconfig.log"
handler = RotatingFileHandler(log_file, maxBytes=1024*1024*50, backupCount=2)
formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
handler.setFormatter(formatter)

# Console handler
console_handler = logging.StreamHandler()
console_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(handler)
logger.addHandler(console_handler)

toml_config = app_config('config.toml')
#nats messagging object
nc = component(toml_config['nats'])

CH_MODE = {"0":"Fuori Scansione", "1":"In Scansione","2": "Scansione e limiti","3": "Trasmiss. evento",
            "4":"Allarme", "5":"Allarme inferiore","6":"Allarme superiore","7":"Non Valido","8":"Canale esportato",
             "17":"Importato Modo 1", "18":"Importato Modo 2","19":"Importato Modo 3",
             "20":"Importato Modo 4","21":"Importato Modo 5","22":"Importato Modo 6"}
CONFIGDIR = 'dirconfig'
DATADIR = 'dirdati'
LASTDIR = 'dirlast'

def get_config(path):
    Engines = {}
    try:
        configure = ConfigParser()
        iset_server_config = configure.read(path)
        for key in configure.sections():
                Engines[key] = {}
                for values in configure[key]:
                    Engines[key][values]= configure[key][values]
        Engines.pop('Sezioni')                    
    except Exception as e:
        logger.error(e)
    return Engines

async def extract_config(path,engine): 
    plant = {}
    with open(path,"rb") as f:
    #async with aiofiles.open(path, 'rb') as f: 
        #Header  reading
        """
        #################
        reading PLANT info
        #################
        """
        IsVirtual = False
        plant["IsVirtual"] = False
        #plant["Type"] = tipo
        plant["Nature"] = int.from_bytes(f.read(1), "little")  
        plant["Engine"] = engine        
        plant["Plant"]  = re.sub(r'[\x00-\x1f]', '', f.read(30).decode('unicode_escape').strip())        
        plant["NPhisic"] = int.from_bytes(f.read(2), "little")     
        plant["NLogic"] = int.from_bytes(f.read(2), "little")   
        plant["Id"] = f"{engine}_{plant['NLogic']}"
        plant["GroupCom"] = int.from_bytes(f.read(2), "little")   
        plant["Stype"] = int.from_bytes(f.read(1), "little")          
        plant["NrAI"] = int.from_bytes(f.read(2), "little")        
        plant["NrAO"] = int.from_bytes(f.read(2), "little")        
        plant["NrDI"] = int.from_bytes(f.read(2), "little")      
        plant["NrDO"] = int.from_bytes(f.read(2), "little")                 
        plant["NrSP"] = int.from_bytes(f.read(2), "little")                 
        plant["NrXP"] = int.from_bytes(f.read(2), "little")                
        plant["NrArch"] = int.from_bytes(f.read(2), "little")                
        plant["NrProto"] = int.from_bytes(f.read(2), "little")         
        plant["IMem"] = struct.unpack('d', f.read(8))[0] 
        plant["IScan"] = struct.unpack('d', f.read(8))[0]
        plant["ITimeout"] = struct.unpack('d', f.read(8))[0]
        plant["Scarico"] = []
        for i in range(6):
            plant["Scarico"].append(int.from_bytes(f.read(4), "little"))

        plant["SAttuatori"]  = re.sub(r'[\x00-\x1f]', '', f.read(64).decode('unicode_escape').strip())   
        plant["TimeOutCom"] = int.from_bytes(f.read(4), "little")
        plant["NRitrasmis"] = int.from_bytes(f.read(1))
        plant["TRitrasmis"] = int.from_bytes(f.read(4), "little") 
        plant["RPortante"] = struct.unpack('d', f.read(8))[0]
        plant["RFineDati"] = struct.unpack('f', f.read(4))[0]
        plant["RByte"] = struct.unpack('f', f.read(4))[0] 
        plant["CfgMask"]  = "M"+re.sub(r'[\x00-\x1f]', '', f.read(64).decode('unicode_escape').strip()) 

        plant["RoutPred"] = []
        for i in range(10):
            plant["RoutPred"].append(int.from_bytes(f.read(2), "little"))
        
        plant["RoutAlt1"] = []
        for i in range(10):
            plant["RoutAlt1"].append(int.from_bytes(f.read(2), "little"))

        plant["RoutAlt2"] = []
        for i in range(10):
            plant["RoutAlt2"].append(int.from_bytes(f.read(2), "little"))

        plant["RoutAlt3"] = []
        for i in range(10):
            plant["RoutAlt3"].append(int.from_bytes(f.read(2), "little") )

        plant["NTelefono"]  = re.sub(r'[\x00-\x1f]', '', f.read(25).decode('unicode_escape').strip())  
        plant["IDCfg"]  = re.sub(r'[\x00-\x1f]', '', f.read(5).decode('unicode_escape').strip())  
        plant["Flags"]  = re.sub(r'[\x00-\x1f]', '', f.read(16).decode('unicode_escape').strip()) 
        plant["Setts"]  = re.sub(r'[\x00-\x1f]', '', f.read(16).decode('unicode_escape').strip())     
        plant["Extras"] = re.sub(r'[\x00-\x1f]', '', f.read(256).decode('unicode_escape').strip())  
        plant["IMemForzata"] = struct.unpack('d', f.read(8))[0]
        plant["Dianomalia"] = int.from_bytes(f.read(2), "little")  
        plant["updatedate"] = datetime.now().timestamp()    
        f.read(144) #dummy read to move cursor

        #save_to_duckdb(plant,"iset","plant")
        """
        #################
        reading IO config
        #################
        """
        topic = f"iset.config.{plant['Engine']}.{plant['NLogic']}.info"
        try:
            await nc.put(topic, json.dumps(plant).encode())
        except Exception as e:
            logger.error(e)
        io_types= {"AIN":plant["NrAI"], "AOUT":plant["NrAO"], "DIN":plant["NrDI"], "DOUT":plant["NrDO"]}
        for k,v in io_types.items():
            #READ_IO
            for i in range(v):
                try:
                    IOlist = {}
                    IOlist["Plant"] = plant["Plant"]
                    IOlist["Engine"] = engine                 
                    IOlist["Type"] = k
                    IOlist["Number"] = int.from_bytes(f.read(2), "little")
                    IOlist["Number"] = i+1 #force to index
                    IOlist["Nome"]  = re.sub(r'[\x00-\x1f]', '', f.read(25).decode('unicode_escape').strip()) 
                    IOlist["UMisura"]  = re.sub(r'[\x00-\x1f]', '', f.read(6).decode('unicode_escape').strip()) 
                    IOlist["Formato"]  = re.sub(r'[\x00-\x1f]', '', f.read(10).decode('unicode_escape').strip()) 
                    IOlist["Gruppo"] =  int.from_bytes(f.read(2), "little")
                    IOlist["SGruppo"] = int.from_bytes(f.read(2), "little")
                    ModoOp = int.from_bytes(f.read(2), "little")
                    IOlist["ModoOp"] = CH_MODE[str(ModoOp)]
                    IOlist["TipoSegnale"] =  int.from_bytes(f.read(2), "little")
                    IOlist["SNormale"] =  int.from_bytes(f.read(2), "little")
                    IOlist["ValorePred"] = struct.unpack('f', f.read(4))[0]
                    IOlist["Sorgente"] =  int.from_bytes(f.read(4), "little")
                    IOlist["InizioScala"] = struct.unpack('f', f.read(4))[0]
                    IOlist["FondoScala"] = struct.unpack('f', f.read(4))[0]
                    IOlist["Offset"] = struct.unpack('f', f.read(4))[0]
                    IOlist["MinimoAmmesso"] = struct.unpack('f', f.read(4))[0]
                    IOlist["MassimoAmmesso"] = struct.unpack('f', f.read(4))[0]
                    IOlist["MinimoVisualizzabile"] = struct.unpack('f', f.read(4))[0]
                    IOlist["MassimoVisualizzabile"] = struct.unpack('f', f.read(4))[0]
                    IOlist["TIntegraz"] = int.from_bytes(f.read(4), "little")
                    IOlist["IsteresiAl"] = struct.unpack('f', f.read(4))[0]
                    IOlist["DMemoriz"] = struct.unpack('f', f.read(4))[0]
                    al_names = ["AllarmeInf","AllarmeSup","PAllarmeInf","PAllarmeSup","AllarmeDInf","AllarmeDSup","AnomaliaSen"]
                    for e in al_names:
                        IOlist[e] = {
                                "Valore" : struct.unpack('f', f.read(4))[0],
                                "LivelloIn" : int.from_bytes(f.read(2), "little"),
                                "LivelloOut": int.from_bytes(f.read(2), "little"),
                                "RitardoIn": int.from_bytes(f.read(4), "little"),
                                "RitardoOut": int.from_bytes(f.read(4), "little"),
                                "MessaggioIn": re.sub(r'[\x00-\x1f]', '', f.read(25).decode('unicode_escape').strip()) ,
                                "MessaggioOut": re.sub(r'[\x00-\x1f]', '', f.read(25).decode('unicode_escape').strip()),
                                "TMemoriz":  int.from_bytes(f.read(4), "little")
                        }

                    IOlist["Flag"]  = re.sub(r'[\x00-\x1f]', '', f.read(8).decode('unicode_escape').strip()) 
                    IOlist["Formula"]  = re.sub(r'[\x00-\x1f]', '', f.read(64).decode('unicode_escape').strip()) 
                    IOlist["TensInf"] = int.from_bytes(f.read(4), "little")
                    IOlist["TensSup"] = int.from_bytes(f.read(4), "little")
                    IOlist["ValoreInf"] = struct.unpack('f', f.read(4))[0]
                    IOlist["ValoreSup"] = struct.unpack('f', f.read(4))[0]
                    IOlist["ValoreSSup"] = struct.unpack('f', f.read(4))[0]   
                    IOlist["ValoreSInf"] = struct.unpack('f', f.read(4))[0]
                    IOlist["Isteresi"] = struct.unpack('f', f.read(4))[0]
                    IOlist["SBancoSSup"]  = re.sub(r'[\x00-\x1f]', '', f.read(16).decode('unicode_escape').strip()) 
                    IOlist["SBancoNorm"]  = re.sub(r'[\x00-\x1f]', '', f.read(16).decode('unicode_escape').strip()) 
                    IOlist["SBancoSInf"]  = re.sub(r'[\x00-\x1f]', '', f.read(16).decode('unicode_escape').strip()) 
                    IOlist["DNormale"]  = re.sub(r'[\x00-\x1f]', '', f.read(15).decode('unicode_escape').strip()) 
                    IOlist["DAllarme"]  = re.sub(r'[\x00-\x1f]', '', f.read(15).decode('unicode_escape').strip()) 
                    IOlist["DestPerif"] = int.from_bytes(f.read(2), "little")
                    IOlist["ExpAddress"]  = re.sub(r'[\x00-\x1f]', '', f.read(24).decode('unicode_escape').strip()) 
                    IOlist["DestCh"] = int.from_bytes(f.read(2), "little")
                    IOlist["TExpNormale"] = int.from_bytes(f.read(4), "little")
                    IOlist["TExpAllarme"] = int.from_bytes(f.read(4), "little")
                    IOlist["Valore"]  = "Null" 
                    IOlist["Descrizione"]  = "Null"                     
                    f.read(1)
                    #tag address
                    T = 0
                    if k == 'AIN':
                        T=0
                    elif k == 'DIN':
                        T=1
                    elif k == 'AOUT':
                        T=2
                    elif k == 'DOUT':
                        T=3

                    index = f"000{IOlist['Number']}"
                    if IOlist['Number'] >= 10:
                        index = f"00{IOlist['Number']}"
                    if IOlist['Number'] >=100:
                        index = f"0{IOlist['Number']}"
                    if IOlist['Number'] >=1000:
                        index = f"{IOlist['Number']}"       
                    topic = f"iset.config.{plant['Engine']}.{plant['NLogic']}.{k}.{IOlist['Number']}"  

                    if  len(IOlist["Nome"]) > 0 and IOlist["Nome"] != 'riserva':                    
                        IOlist["Tag"] = f"{engine}::{plant['NLogic']}0{T}{index}"                 
                        topic = f"iset.config.{plant['Engine']}.{plant['NLogic']}.{k}.{IOlist['Number']}"
                        try:
                            #print(topic)
                            await nc.put(topic, json.dumps(IOlist).encode())
                        except Exception as e:
                            logger.error(e)
                except Exception as e:
                    logger.error(f"{e},{plant['Engine']}.{plant['NLogic']}.{i} ")

        #setpoints
        for i in range(plant["NrSP"]):
            try:
                #Natura  = re.sub(r'[\x00-\x1f]', '', f.read(1)
                IOlist = {}
                IOlist["Plant"] = plant["Plant"]
                IOlist["Engine"] = engine 
                int.from_bytes(f.read(2), "little")
                IOlist["Number"] = i+1
                IOlist["Type"] = "SP"
                IOlist["Nome"]  = re.sub(r'[\x00-\x1f]', '', f.read(25).decode('unicode_escape').strip()) 
                IOlist["UMisura"]  = re.sub(r'[\x00-\x1f]', '', f.read(6).decode('unicode_escape').strip()) 
                IOlist["Formato"]  = re.sub(r'[\x00-\x1f]', '', f.read(10).decode('unicode_escape').strip()) 
                IOlist["Gruppo"] =  int.from_bytes(f.read(2), "little")
                IOlist["SGruppo"] = int.from_bytes(f.read(2), "little")
                IOlist["ModoOp"] =  str(int.from_bytes(f.read(2), "little"))
                IOlist["TipoSegnale"] =  int.from_bytes(f.read(2), "little")     
                f.read(10).decode('unicode_escape').strip().strip() #IOlist["ValorePred"] 
                try:
                    IOlist["MinimoAmmesso"]  = float(re.sub(r'[\x00-\x1f]', '', f.read(10).decode('unicode_escape').strip()))
                    IOlist["MassimoAmmesso"]  = float(re.sub(r'[\x00-\x1f]', '', f.read(10).decode('unicode_escape').strip()))
                except:
                    pass
                IOlist["Flag"]  = re.sub(r'[\x00-\x1f]', '', f.read(8).decode('unicode_escape').strip()) 
                IOlist["Valore"]  = re.sub(r'[\x00-\x1f]', '', f.read(10).decode('unicode_escape').strip()) 
                f.read(40)
                IOlist["Descrizione"]  = re.sub(r'[\x00-\x1f]', '', f.read(128).decode('unicode_escape').strip()) 
                f.read(539)
                #tag address
                T = 4
                index = f"000{IOlist['Number']}"
                if IOlist['Number'] >= 10:
                    index = f"00{IOlist['Number']}"
                elif IOlist['Number'] >=100:
                    index = f"0{IOlist['Number']}"
                elif IOlist['Number'] >=1000:
                    index = f"{IOlist['Number']}"
                
                if  len(IOlist["Nome"]) > 0 and IOlist["Nome"] != 'riserva':
                    IOlist["Tag"] = f"{engine}::{plant['NLogic']}0{T}{index}"             
                    topic = f"iset.config.{plant['Engine']}.{plant['NLogic']}.SP.{IOlist['Number']}"
                    try:
                        await nc.put(topic, json.dumps(IOlist).encode())
                    except Exception as e:
                        logger.error(e)

            except Exception as e:
                logger.error(f"extraction {e}")
                pass
    #save_to_duckdb(data,"iset","ioconfig")

async def get_compsis(path, engine):
    devicelist = {}
    files = list()
    with open(path, "r") as f:
        devices = csv.reader(f)
        
        for lines in devices:
            try:
                name = lines[1]
                if 'Numero periferiche' not in lines:
                    logical_n = int(lines[2].strip()) 
                    devicelist[name] = {"Engine": engine, 
                                        "NLogic": logical_n, 
                                        "NrAI": int(lines[3].strip()),
                                        "NrDI": int(lines[4].strip()),
                                        "NrAO": int(lines[5].strip()),
                                        "NrDO": int(lines[6].strip()),
                                        "NrSP": int(lines[10].strip())}
                    
                    filename = f"P{logical_n}.cfg"
                    if logical_n >= 100:
                        filename = f"P0{logical_n}.cfg"
                    elif logical_n >= 10:
                        filename = f"P00{logical_n}.cfg"    
                    elif logical_n >= 0:
                        filename = f"P000{logical_n}.cfg"                                    
                    
                    files.append(filename)
            except Exception as e:
                logging.error(e)

    topic = f"iset.config.{engine}.info"
    try:
        await nc.put(topic, json.dumps(devicelist).encode())
    except Exception as e:
        logger.error(e)

    return (files,devicelist)

async def configloop():    
    await nc.connect( bucket = 'iset')  
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    old_ts = dict()
    async def cb(msg):
        logger.info('richiesta sincronizzazione mappa iset ')
        try:
            keys = await nc.keys()     
            matching_keys = [key for key in keys if '.config.' in key]
            keys_to_delete = []

            for engine,v in cfg.items():
                if engine != "SQLGLOG" and engine != "RVP":  #!= "SQLGLOG"                    
                    compsis = f"{v[CONFIGDIR]}compsis.cfg"
                    cfg_files,compsiscfg = await get_compsis(compsis,engine)          

                    for key,info in compsiscfg.items():
                        ain = info['NrAI']
                        din = info['NrDI']
                        dout = info['NrDO']
                        aout = info['NrAO']
                        sepoints = info['NrSP']

                        tempkey = f"iset.config.{info['Engine']}.info"
                        if tempkey in matching_keys: 
                            matching_keys.remove(tempkey)

                        tempkey = f"iset.config.{info['Engine']}.{info['NLogic']}.info"
                        if tempkey in matching_keys: 
                            matching_keys.remove(tempkey)

                        for i in range(ain):
                            tempkey = f"iset.config.{info['Engine']}.{info['NLogic']}.AIN.{i}"
                            if tempkey in matching_keys: 
                                matching_keys.remove(tempkey)
                        for i in range(din):
                            tempkey = f"iset.config.{info['Engine']}.{info['NLogic']}.DIN.{i}"
                            if tempkey in matching_keys: 
                                matching_keys.remove(tempkey)         
                        for i in range(dout):
                            tempkey = f"iset.config.{info['Engine']}.{info['NLogic']}.DOUT.{i}"
                            if tempkey in matching_keys: 
                                matching_keys.remove(tempkey)       
                        for i in range(sepoints):
                            tempkey = f"iset.config.{info['Engine']}.{info['NLogic']}.SP.{i}"
                            if tempkey in matching_keys: 
                                matching_keys.remove(tempkey)     

            keys_del = [key for key in matching_keys if 'iset.config.' in key]
            for key in keys_del:
                await nc.delete(key)
            msg.respond(b'done')
            logger.info('fine sincronizzazione mappa iset ')
        except Exception as e:
            logger.error(e)
            msg.respond(str(e).encode())

    await nc.sub(f'webscada.request.syncconfig', cb = cb)

    while True:
        for engine,v in cfg.items():
            if engine != "SQLGLOG" and engine != "RVP":  #!= "SQLGLOG"
                try:
                    compsis = f"{v[CONFIGDIR]}compsis.cfg"
                    file_ts = os.path.getmtime(compsis)
                    if (compsis in old_ts):
                        if old_ts[compsis] == file_ts:  
                            continue

                    logger.info(f"{compsis} modificato")
                    cfg_files = await get_compsis(compsis,engine)  
                    for file in cfg_files[0]:

                        file_path = f"{v[CONFIGDIR]}{file}"  
                        try:                
                            file_ts = os.path.getmtime(file_path)
                        except:
                            #workaround for missing file, needs to be improved
                            file_ts = datetime.now().timestamp() 
                            old_ts[file] = file_ts                            

                        if (file in old_ts):
                            if old_ts[file] == file_ts:                            
                                continue
                        try:
                            logger.info(f"{file_path} modificato")
                            await extract_config(file_path,engine)
                            old_ts[file] = file_ts
                        except Exception as e:
                            logger.error(f"path {e},{file_path}")
                                
                    old_ts[compsis] = os.path.getmtime(compsis)

                except Exception as e:
                    logger.error(f"{e},{compsis}")
                    continue
            await asyncio.sleep(1) 

if __name__ == "__main__":
    asyncio.run(configloop())        