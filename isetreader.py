import multiprocessing as mp
from isetalarms import alarms
#from isetconfigpolling import configloop 
from isetconfigsync import configloop 
from isetwatchpolling import isetwatch
from isetapi import isetapi_runner
from crossreference import crossref_runner
from sirapi import sirapi_runner
import asyncio
import time
import schedule
from iset import get_hist_task
from isetmap import get_iset_map
from opcservice import opc_loop
import os
import sys
def config_runner():
    asyncio.run(configloop())

def alarms_runner():
    asyncio.run(alarms())

def opc_runner():
    asyncio.run(opc_loop())


def main():
    schedule.every().day.at("00:15").do(crossref_runner)


    schedule.every().day.at("00:30").do(get_iset_map)


    schedule.every().day.at("01:00").do(get_hist_task)
    
    sirapi_process = mp.Process(target = sirapi_runner, daemon = True)
    sirapi_process.start()

    isetapi_process = mp.Process(target = isetapi_runner, daemon = True)
    isetapi_process.start()

    time.sleep(5)
    configloop_process = mp.Process(target = config_runner, daemon = True)
    configloop_process.start()
    
    runner_process = mp.Process(target = isetwatch, daemon = True)
    runner_process.start()    

    alarms_process = mp.Process(target = alarms_runner, daemon = True)
    alarms_process.start()

    opc_process = mp.Process(target = opc_runner, daemon = True)
    opc_process.start()
        
    #crossref_process = mp.Process(target = crossref_runner, daemon = True)
    #scrossref_process.start()

    while True:
        try:
            schedule.run_pending()
            if not isetapi_process.is_alive():
                print("isetapi_process died, restarting...")
                isetapi_process = mp.Process(target = isetapi_runner, daemon = True)
                isetapi_process.start()
        except:
            pass
        time.sleep(1)

if __name__ == '__main__':
    main()