import struct
from datetime import datetime
import logging 
import os
import time
import glob
from iset import  get_config,extract_hist_numpy
from config import app_config

toml_config = app_config('config.toml')
CONFIGDIR = 'dirconfig'
DATADIR = 'dirdati'
LASTDIR = 'dirlast'

def hisotrian():    
    inipath = toml_config['iset'].get('isetini','iset.ini')
    cfg = get_config(inipath)
    old_ts = dict()
    while True:

        for engine,v in cfg.items():          
            if engine != "SQLGLOG": 
                try:
                    engine_path = v[DATADIR]
                    year = datetime.now().year-1
                    this_month = datetime.now().month  
                    for month in range(1, 13):
                        if this_month != month:
                            pass
                        wildcard = f"{engine_path}{year}\*.{month:X}*"
                        all_files = glob.glob(wildcard, recursive=True)

                        for hist_file in all_files:
                            file_ts = os.path.getmtime(hist_file)
                            if (hist_file in old_ts):
                                if old_ts[hist_file] == file_ts:  
                                    continue

                            extract_hist_numpy(engine,[hist_file],year,month)
                            print(f"{hist_file} modificato")
                            old_ts[hist_file] = os.path.getmtime(hist_file)
                            time.sleep(0.1) 
                except Exception as e:
                    logging.debug(e)
                    continue
            time.sleep(1) 

if __name__ == "__main__":  
    hisotrian()
