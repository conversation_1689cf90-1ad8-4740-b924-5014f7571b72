version: '3.8'

services:
  # Main ISET Reader application
  isetreader:
    build: .
    container_name: iset-reader
    depends_on:
      nats:
        condition: service_healthy
    volumes:
      # Mount config file
      - ./config.toml:/app/config.toml:ro
      # Mount log directory for persistence
      - ./log:/app/log
      # Mount any data directories if needed (adjust paths as needed)
      # - /path/to/iset/data:/data:ro
    networks:
      - iset-network
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    # Uncomment if you need to expose ports
    # ports:
    #   - "8000:8000"
    
    # Health check for the application
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  nats_data:
    driver: local

networks:
  iset-network:
    driver: bridge
