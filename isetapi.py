import asyncio
from datetime import datetime
from messaging import component
from iset import read_hist, read_hist_parquet
import json
import duckdb
import fsspec
import logging
from logging.handlers import RotatingFileHandler
import os
from config import app_config

toml_config = app_config('config.toml')
"""
Iset archive service responding to requests
"""
# create log folder
if not os.path.isdir("log"):
    os.makedirs('log')

#logger
logger = logging.getLogger('isetapi')
logger.setLevel(logging.INFO)

# Create a rotating file handler
log_file = f"./log/isetapi.log"
handler = RotatingFileHandler(log_file, maxBytes=1024*1024*50, backupCount=2)
formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
handler.setFormatter(formatter)

# Console handler
console_handler = logging.StreamHandler()
console_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
console_handler.setFormatter(console_formatter)

logger.addHandler(handler)
logger.addHandler(console_handler)

async def loop():
    nc= component(toml_config['nats'])
    await nc.connect()
    
    async def cb(msg):
        logger.info(f'iset hist request {msg.data.decode()} on "{msg.subject}"')

        try:
            req = json.loads(msg.data.decode().replace("'",'"'))
            tags = req['tags']
            start = req['start_date']
            end = req['end_date']
            start_date = datetime.strptime(start,"%Y-%m-%d %H:%M:%S") 
            end_date = datetime.strptime(end,"%Y-%m-%d %H:%M:%S")              
            parquet = req.get('parquet',False)
            query = req.get('query',None)
            if parquet:
                dataparquet = await asyncio.to_thread(read_hist_parquet,tags,start_date,end_date,query)
                await msg.respond(dataparquet) 
                logger.info(f"parquet size {len(dataparquet)}")
            else:
                #data = read_hist(tags,start_date,end_date) 
                data = await asyncio.to_thread(read_hist,tags,start_date,end_date)
                resp = json.dumps(data).encode()
                await msg.respond(resp)
                logger.info(f"json size {len(resp)}")

        except Exception as e:
            logger.error(e)
            try:
                await msg.respond(json.dumps({'error':str(e)}).encode())
            except Exception as e:
                logger.warning(e)


    await nc.sub('iset.api.histchart', cb= cb)#iset.api.histchart  iset.api.hist(for Elia)
    await nc.sub('iset.api.hist', cb= cb)
    logger.info( 'iset hist api started and listening on "iset.api.hist" & "iset.api.histchart"')
    while True:
        #idle
        await asyncio.sleep(1)

def isetapi_runner():   
    asyncio.run(loop())

if __name__ == '__main__':
    asyncio.run(loop())