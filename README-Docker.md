# ISET Reader Docker Setup

This Docker Compose setup provides a containerized environment for running the ISET Reader application with all its dependencies.

## Services

### 1. NATS Server (`nats`)
- **Purpose**: Message broker for inter-service communication
- **Ports**: 
  - `4222`: NATS client connections
  - `8222`: HTTP monitoring interface
  - `6222`: Cluster port
- **Features**: JetStream enabled for persistent messaging
- **Health Check**: HTTP endpoint monitoring

### 2. ISET Reader (`isetreader`)
- **Purpose**: Main application that processes ISET data
- **Dependencies**: NATS server
- **Volumes**: 
  - Config file mounted read-only
  - Log directory for persistent logging
- **Health Check**: Python process validation

## Prerequisites

1. **Docker** and **Docker Compose** installed
2. **Configuration file**: Ensure `config.toml` is properly configured
3. **Network access**: If your application needs to access network paths (like `\\*************\...`)

## Quick Start

### 1. Basic Setup
```bash
# Build and start the services
docker-compose up -d

# View logs
docker-compose logs -f isetreader

# Stop services
docker-compose down
```

### 2. With Debug Tools
```bash
# Start with DuckDB web interface
docker-compose --profile debug up -d

# Access DuckDB web interface at http://localhost:8080
```

### 3. Development Mode
```bash
# Build and run with live logs
docker-compose up --build

# Rebuild only the application
docker-compose build isetreader
docker-compose up -d isetreader
```

## Configuration

### Network Paths
If your application accesses network paths (like `\\*************\C_Server1_Scada\...`), you may need to:

1. **Mount network drives** as volumes in docker-compose.yml:
```yaml
volumes:
  - /path/to/mounted/network/drive:/network:ro
```

2. **Update config.toml** to use container paths:
```toml
[iset]
isetini = "/network/Windows/iset.ini"
isetgui = "/network/Iset/Idro.NetWork/Data/DR/Gui/"
```

### Environment Variables
Add environment variables in docker-compose.yml if needed:
```yaml
environment:
  - CUSTOM_VAR=value
  - DEBUG=true
```

## Monitoring

### NATS Monitoring
- Web interface: http://localhost:8222
- Check server status, connections, and JetStream

### Application Logs
```bash
# Follow application logs
docker-compose logs -f isetreader

# View NATS logs
docker-compose logs -f nats

# View all logs
docker-compose logs -f
```

### Health Checks
```bash
# Check service health
docker-compose ps

# Inspect specific service
docker inspect iset-reader
```

## Troubleshooting

### Common Issues

1. **NATS Connection Failed**
   - Check if NATS service is healthy: `docker-compose ps`
   - Verify network connectivity between services

2. **File Access Issues**
   - Ensure proper volume mounts for data directories
   - Check file permissions (user ID 1000 in container)

3. **Configuration Issues**
   - Verify config.toml is properly mounted
   - Check paths in configuration match container paths

### Debugging Commands
```bash
# Enter application container
docker-compose exec isetreader bash

# Check application processes
docker-compose exec isetreader ps aux

# Test NATS connectivity
docker-compose exec isetreader python -c "import nats; print('NATS import successful')"

# View container resource usage
docker stats
```

## Data Persistence

- **NATS data**: Stored in `nats_data` Docker volume
- **Application logs**: Mounted to `./log` directory
- **Database files**: Consider mounting database directory for persistence

## Security Considerations

- Application runs as non-root user (UID 1000)
- Network isolation using Docker bridge network
- Read-only mounts for configuration files
- Consider adding secrets management for sensitive data

## Scaling

To run multiple instances of the application:
```bash
docker-compose up -d --scale isetreader=3
```

Note: Ensure your application can handle multiple instances and shared resources properly.

## Maintenance

### Updates
```bash
# Pull latest images
docker-compose pull

# Rebuild and restart
docker-compose up -d --build
```

### Cleanup
```bash
# Remove containers and networks
docker-compose down

# Remove containers, networks, and volumes
docker-compose down -v

# Remove unused Docker resources
docker system prune
```
