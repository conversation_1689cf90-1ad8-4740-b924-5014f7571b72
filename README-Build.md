# ISET Reader Build Instructions

This document explains how to build the ISET Reader application into a single executable file using PyInstaller.

## Prerequisites

1. **Python 3.11+** installed
2. **All dependencies** installed (see `requirements.txt`)
3. **PyInstaller** (will be installed automatically by build scripts)
4. **UPX** (optional, for compression)

## Build Files

- **`isetreader.spec`** - PyInstaller specification file with all configurations
- **`build.ps1`** - PowerShell build script with advanced options
- **`build.bat`** - Simple batch file for basic building
- **`requirements.txt`** - Python dependencies

## Quick Build

### Using PowerShell (Recommended)
```powershell
# Standard build
.\build.ps1

# Clean build (removes previous build artifacts)
.\build.ps1 -Clean

# Debug build with verbose output
.\build.ps1 -Debug -NoUPX
```

### Using Batch File
```cmd
# Standard build
build.bat

# Clean build
build.bat clean
```

### Manual Build
```bash
# Install PyInstaller if not already installed
pip install pyinstaller

# Build using the spec file
pyinstaller --clean --noconfirm --onefile isetreader.spec
```

## Build Options

### PowerShell Script Options
- **`-Clean`** - Remove build directories before building
- **`-Debug`** - Build with debug information and verbose output
- **`-NoUPX`** - Disable UPX compression (useful for debugging)
- **`-Help`** - Show usage information

### Spec File Configuration

The `isetreader.spec` file includes:

#### **Hidden Imports**
- All required modules that PyInstaller might miss
- Multiprocessing support modules
- Async/await related modules
- NATS messaging modules
- DuckDB and data processing modules

#### **Data Files**
- FSSpec implementations
- DuckDB extensions
- NATS configuration files

#### **Exclusions**
- GUI frameworks (tkinter, PyQt, etc.)
- Development tools (IPython, Jupyter)
- Unnecessary packages to reduce file size

#### **Multiprocessing Support**
- Proper freeze support for multiprocessing
- Windows-specific async event loop handling

## Build Output

After a successful build, you'll find:

```
dist/
├── isetreader.exe     # Main executable
├── config.toml        # Configuration file (copied automatically)
└── log/               # Log directory (created automatically)
```

## Configuration

### External Configuration
The `config.toml` file is kept **external** to the executable, allowing you to:
- Modify configuration without rebuilding
- Use different configurations for different environments
- Update network paths and credentials easily

### Configuration Location
The application looks for `config.toml` in:
1. Same directory as the executable (when compiled)
2. Current working directory (when running as script)

## Troubleshooting

### Common Issues

#### **1. Missing Modules**
```
ModuleNotFoundError: No module named 'xyz'
```
**Solution**: Add the missing module to `hiddenimports` in `isetreader.spec`

#### **2. Multiprocessing Issues**
```
RuntimeError: An attempt has been made to start a new process...
```
**Solution**: The spec file includes `mp.freeze_support()` - ensure it's called

#### **3. Large File Size**
**Solutions**:
- Enable UPX compression (default)
- Add more exclusions to the spec file
- Use `--exclude-module` for unnecessary packages

#### **4. Slow Startup**
**Solutions**:
- Build without debug information
- Enable UPX compression
- Consider using `--onedir` instead of `--onefile`

### Debug Build
For troubleshooting, use debug build:
```powershell
.\build.ps1 -Debug -NoUPX
```

This provides:
- Verbose console output
- Detailed import information
- No compression for faster builds
- Debug symbols

### Testing the Build
```cmd
cd dist
.\isetreader.exe
```

Check that:
- Configuration is loaded correctly
- All processes start without errors
- Network connections work
- Logging functions properly

## Advanced Configuration

### Custom Spec Modifications

To modify the build process, edit `isetreader.spec`:

#### **Add Hidden Imports**
```python
hiddenimports = [
    'your_module_here',
    # ... existing imports
]
```

#### **Include Data Files**
```python
datas = [
    ('path/to/data', 'destination'),
    # ... existing data files
]
```

#### **Exclude Modules**
```python
excludes = [
    'unwanted_module',
    # ... existing exclusions
]
```

### Build Optimization

#### **Reduce File Size**
1. Add more exclusions
2. Enable UPX compression
3. Remove unused dependencies

#### **Improve Performance**
1. Use `--onedir` for faster startup
2. Disable debug information
3. Enable optimizations

## Deployment

### Single File Deployment
The default build creates a single executable file:
- **Pros**: Easy to distribute, self-contained
- **Cons**: Slower startup, larger memory usage

### Directory Deployment
For better performance, use `--onedir`:
```python
# In isetreader.spec, change:
exe = EXE(
    # ... other parameters
    onefile=False,  # Change to False
)
```

### Distribution Package
Create a distribution package:
```
isetreader-package/
├── isetreader.exe
├── config.toml
├── log/
└── README.txt
```

## Maintenance

### Updating Dependencies
1. Update `requirements.txt`
2. Rebuild the application
3. Test thoroughly

### Version Management
Consider adding version information to the spec file:
```python
# Add to exe = EXE(...) parameters
version='version.txt',
```

### Automated Builds
For CI/CD, use the batch file or PowerShell script:
```yaml
# GitHub Actions example
- name: Build Application
  run: .\build.ps1 -Clean
```

## Performance Notes

- **Startup Time**: ~5-10 seconds for first run (extraction)
- **Memory Usage**: Higher than script version due to bundling
- **File Size**: Typically 50-100MB depending on dependencies
- **Runtime Performance**: Same as script version after startup
